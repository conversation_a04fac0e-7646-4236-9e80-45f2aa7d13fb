import { Currency } from '../shared/enums';
export declare enum OrderStatus {
    draft = "draft",
    pending = "pending",
    confirmed = "confirmed",
    processing = "processing",
    shipped = "shipped",
    delivered = "delivered",
    cancelled = "cancelled",
    returned = "returned"
}
export declare enum OrderPriority {
    low = "low",
    normal = "normal",
    high = "high",
    urgent = "urgent"
}
export declare class Order {
    id: string;
    status: OrderStatus;
    priority: OrderPriority;
    orderNumber: string;
    subtotal: number;
    useTax: boolean;
    taxRate: number;
    taxAmount: number;
    total: number;
    currency: Currency;
    cancellationReason: string;
    orderDate: Date;
    expectedDeliveryDate: Date;
    preferredDeliveryLocation: string;
    createdAt: Date;
    updatedAt: Date;
    isDeleted: boolean;
    createdBy: string;
    updatedBy: string;
    userId: string;
    storeId: string;
    customerId: string;
    store: any;
    customer: any;
    createdByUser: any;
    updatedByUser: any;
    user: any;
    orderItems: any[];
}
