export declare function placeOrderTool(params: {
    customerName: string;
    customerEmail?: string;
    customerPhone: string;
    customerAddress: string;
    items: Array<{
        productId?: string | number | bigint;
        productName?: string;
        quantity: number;
        taxAmount?: number;
    }>;
    useTax?: boolean;
    taxRate?: number;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
}, db: any, conversationUuid: string): Promise<{
    message: string;
    orderNumber: any;
    orderId: any;
    underway: boolean;
    total: any;
    customerName: any;
    customerPhone: any;
    status: any;
    priority: any;
    items: any;
    trackingUrl: string;
} | {
    message: string;
    orderNumber: any;
    orderId: any;
    total: any;
    trackingUrl: string;
    underway?: undefined;
    customerName?: undefined;
    customerPhone?: undefined;
    status?: undefined;
    priority?: undefined;
    items?: undefined;
}>;
export declare const placeOrder: any;
