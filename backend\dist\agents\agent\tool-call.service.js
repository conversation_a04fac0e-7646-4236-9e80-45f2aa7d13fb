"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createToolCall = createToolCall;
exports.updateToolCall = updateToolCall;
exports.getToolCallsByConversation = getToolCallsByConversation;
exports.getToolCalls = getToolCalls;
exports.getToolCallById = getToolCallById;
exports.deleteToolCall = deleteToolCall;
exports.getToolCallStats = getToolCallStats;
const ToolCall_1 = require("../entities/ToolCall");
const Message_1 = require("../entities/Message");
const Conversation_1 = require("../entities/Conversation");
async function createToolCall(db, input) {
    const { toolName, toolInput, conversationId, createdBy, toolOutput, executionTime, success = true, errorMessage, cost, inputTokens, outputTokens } = input;
    try {
        const messageRepository = db.getRepository(Message_1.Message);
        const toolCallRepository = db.getRepository(ToolCall_1.ToolCall);
        const conversationRepository = db.getRepository(Conversation_1.Conversation);
        const [lastMessage, lastToolCall] = await Promise.all([
            messageRepository.findOne({
                where: { conversationId, isDeleted: false },
                order: { sequence: 'DESC' },
                select: ['sequence'],
            }),
            toolCallRepository.findOne({
                where: { conversationId, isDeleted: false },
                order: { sequence: 'DESC' },
                select: ['sequence'],
            }),
        ]);
        const maxSequence = Math.max(lastMessage?.sequence ?? 0, lastToolCall?.sequence ?? 0);
        const nextSequence = maxSequence + 1;
        const toolCall = await toolCallRepository.save({
            toolName,
            toolInput,
            conversationId,
            createdBy,
            toolOutput,
            executionTime,
            success,
            errorMessage,
            cost,
            inputTokens,
            outputTokens,
            sequence: nextSequence,
        });
        await conversationRepository.update({ id: conversationId }, {
            updatedAt: new Date(),
        });
        return toolCall;
    }
    catch (error) {
        console.error('Failed to create tool call record:', error);
        throw new Error(`Failed to create tool call record: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function updateToolCall(db, input) {
    const { id, toolOutput, executionTime, success, errorMessage, cost, inputTokens, outputTokens, updatedBy } = input;
    try {
        const toolCallRepository = db.getRepository(ToolCall_1.ToolCall);
        const toolCall = await toolCallRepository.update({
            id,
            isDeleted: false,
        }, {
            toolOutput,
            executionTime,
            success,
            errorMessage,
            cost,
            inputTokens,
            outputTokens,
            updatedBy,
            updatedAt: new Date(),
        });
        return toolCall;
    }
    catch (error) {
        console.error('Failed to update tool call record:', error);
        throw new Error(`Failed to update tool call record: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCallsByConversation(db, conversationId, limit, offset) {
    try {
        const toolCallRepository = db.getRepository(ToolCall_1.ToolCall);
        const toolCalls = await toolCallRepository.find({
            where: {
                conversationId,
                isDeleted: false,
            },
            order: {
                createdAt: 'ASC',
            },
            take: limit,
            skip: offset,
        });
        return toolCalls;
    }
    catch (error) {
        console.error('Failed to get tool calls for conversation:', error);
        throw new Error(`Failed to get tool calls for conversation: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCalls(db, filter, limit, offset) {
    try {
        const toolCallRepository = db.getRepository(ToolCall_1.ToolCall);
        const where = {
            isDeleted: false,
        };
        if (filter.conversationId) {
            where.conversationId = filter.conversationId;
        }
        if (filter.toolName) {
            where.toolName = filter.toolName;
        }
        if (filter.success !== undefined) {
            where.success = filter.success;
        }
        if (filter.createdAfter || filter.createdBefore) {
            where.createdAt = {};
            if (filter.createdAfter) {
                where.createdAt.gte = filter.createdAfter;
            }
            if (filter.createdBefore) {
                where.createdAt.lte = filter.createdBefore;
            }
        }
        const toolCalls = await toolCallRepository.find({
            where,
            order: {
                createdAt: 'DESC',
            },
            take: limit,
            skip: offset,
        });
        return toolCalls;
    }
    catch (error) {
        console.error('Failed to get tool calls:', error);
        throw new Error(`Failed to get tool calls: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCallById(db, id) {
    try {
        const toolCallRepository = db.getRepository(ToolCall_1.ToolCall);
        const toolCall = await toolCallRepository.findOne({
            where: {
                id,
                isDeleted: false,
            },
        });
        return toolCall;
    }
    catch (error) {
        console.error('Failed to get tool call by ID:', error);
        throw new Error(`Failed to get tool call by ID: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function deleteToolCall(db, id, deletedBy) {
    try {
        const toolCallRepository = db.getRepository(ToolCall_1.ToolCall);
        await toolCallRepository.update({
            id,
            isDeleted: false,
        }, {
            isDeleted: true,
            updatedBy: deletedBy,
            updatedAt: new Date(),
        });
        return true;
    }
    catch (error) {
        console.error('Failed to delete tool call:', error);
        throw new Error(`Failed to delete tool call: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCallStats(db, conversationId) {
    try {
        const toolCallRepository = db.getRepository(ToolCall_1.ToolCall);
        const toolCalls = await toolCallRepository.find({
            where: {
                conversationId,
                isDeleted: false,
            },
            select: ['toolName', 'success', 'executionTime'],
        });
        const total = toolCalls.length;
        const successful = toolCalls.filter((tc) => tc.success).length;
        const failed = total - successful;
        const executionTimes = toolCalls
            .filter((tc) => tc.executionTime !== null)
            .map((tc) => tc.executionTime);
        const averageExecutionTime = executionTimes.length > 0
            ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
            : null;
        const toolUsage = {};
        toolCalls.forEach((tc) => {
            toolUsage[tc.toolName] = (toolUsage[tc.toolName] || 0) + 1;
        });
        return {
            total,
            successful,
            failed,
            averageExecutionTime,
            toolUsage,
        };
    }
    catch (error) {
        console.error('Failed to get tool call stats:', error);
        throw new Error(`Failed to get tool call stats: ${error instanceof Error ? error.message : String(error)}`);
    }
}
//# sourceMappingURL=tool-call.service.js.map