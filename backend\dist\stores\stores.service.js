"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoresService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const store_entity_1 = require("./store.entity");
const enums_1 = require("../shared/enums");
let StoresService = class StoresService {
    constructor(storesRepository) {
        this.storesRepository = storesRepository;
    }
    async findAll() {
        return this.storesRepository.find({
            where: { isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const store = await this.storesRepository.findOne({
            where: { id, isDeleted: false },
        });
        if (!store) {
            throw new common_1.NotFoundException(`Store with ID ${id} not found`);
        }
        return store;
    }
    async findByUuid(uuid) {
        throw new common_1.NotFoundException(`Store with UUID ${uuid} not found`);
    }
    async findByUserId(userId) {
        return this.storesRepository.find({
            where: [
                { ownerId: userId, isDeleted: false },
                { managerId: userId, isDeleted: false }
            ],
            order: { createdAt: 'DESC' },
        });
    }
    async create(createStoreDto) {
        const { userId, ...storeData } = createStoreDto;
        const store = new store_entity_1.Store();
        Object.assign(store, {
            ...storeData,
            createdBy: userId,
            ownerId: userId,
            status: enums_1.StoreStatus.ACTIVE,
            isDeleted: false
        });
        return await this.storesRepository.save(store);
    }
    async update(id, updateStoreDto) {
        const store = await this.findOne(id);
        Object.assign(store, updateStoreDto);
        return this.storesRepository.save(store);
    }
    async remove(id) {
        const store = await this.findOne(id);
        store.isDeleted = true;
        await this.storesRepository.save(store);
    }
};
exports.StoresService = StoresService;
exports.StoresService = StoresService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(store_entity_1.Store)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], StoresService);
//# sourceMappingURL=stores.service.js.map