#!/usr/bin/env tsx
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
const path_1 = require("path");
const fs_1 = require("fs");
(0, dotenv_1.config)();
async function generateMigration() {
    const args = process.argv.slice(2);
    const migrationName = args.find(arg => arg.startsWith('--name='))?.split('=')[1] || 'Migration';
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'teno_store_db',
        synchronize: false,
        logging: false,
        entities: [(0, path_1.join)(__dirname, '../**/*.entity{.ts,.js}')],
        migrations: [(0, path_1.join)(__dirname, '../migrations/*{.ts,.js}')],
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });
    try {
        await dataSource.initialize();
        console.log('✅ Database connection established');
        const sqlInMemory = await dataSource.driver.createSchemaBuilder().log();
        if (sqlInMemory.upQueries.length === 0) {
            console.log('ℹ️  No schema changes detected');
            return;
        }
        const migrationsDir = (0, path_1.join)(__dirname, '../migrations');
        if (!(0, fs_1.existsSync)(migrationsDir)) {
            (0, fs_1.mkdirSync)(migrationsDir, { recursive: true });
        }
        const timestamp = Date.now();
        const filename = `${timestamp}-${migrationName}.ts`;
        const filepath = (0, path_1.join)(migrationsDir, filename);
        const migrationContent = `import { MigrationInterface, QueryRunner } from "typeorm";

export class ${migrationName}${timestamp} implements MigrationInterface {
    name = '${migrationName}${timestamp}'

    public async up(queryRunner: QueryRunner): Promise<void> {
${sqlInMemory.upQueries.map(query => `        await queryRunner.query(\`${query.query}\`);`).join('\n')}
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
${sqlInMemory.downQueries.map(query => `        await queryRunner.query(\`${query.query}\`);`).join('\n')}
    }
}
`;
        (0, fs_1.writeFileSync)(filepath, migrationContent);
        console.log(`✅ Migration generated: ${filename}`);
        console.log(`📁 Location: ${filepath}`);
        console.log(`🔄 Up queries: ${sqlInMemory.upQueries.length}`);
        console.log(`↩️  Down queries: ${sqlInMemory.downQueries.length}`);
        await dataSource.destroy();
        console.log('✅ Database connection closed');
    }
    catch (error) {
        console.error('❌ Migration generation failed:', error);
        process.exit(1);
    }
}
generateMigration();
//# sourceMappingURL=generate-migration.js.map