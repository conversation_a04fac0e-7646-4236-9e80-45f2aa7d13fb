"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const typeorm_1 = require("typeorm");
const enums_1 = require("../shared/enums");
let User = class User {
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('increment', { type: 'bigint' }),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', unique: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: enums_1.UserRole, default: enums_1.UserRole.STAFF }),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: enums_1.UserStatus, default: enums_1.UserStatus.ACTIVE }),
    __metadata("design:type", String)
], User.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "state", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "zipCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "lastLoginAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isDeleted", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Store', (store) => store.owner),
    __metadata("design:type", Array)
], User.prototype, "ownedStores", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Store', (store) => store.manager),
    __metadata("design:type", Array)
], User.prototype, "managedStores", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Order', (order) => order.user),
    __metadata("design:type", Array)
], User.prototype, "orders", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Order', (order) => order.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdOrders", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Order', (order) => order.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedOrders", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('OrderItem', (orderItem) => orderItem.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdOrderItems", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('OrderItem', (orderItem) => orderItem.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedOrderItems", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Product', (product) => product.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdProducts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Product', (product) => product.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedProducts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Customer', (customer) => customer.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdCustomers", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Customer', (customer) => customer.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedCustomers", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Store', (store) => store.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdStores", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Store', (store) => store.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedStores", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Conversation', (conversation) => conversation.user),
    __metadata("design:type", Array)
], User.prototype, "conversations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Message', (message) => message.user),
    __metadata("design:type", Array)
], User.prototype, "messages", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Agent', (agent) => agent.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdAgents", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Agent', (agent) => agent.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedAgents", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('ToolCall', (toolCall) => toolCall.user),
    __metadata("design:type", Array)
], User.prototype, "toolCalls", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('ToolCall', (toolCall) => toolCall.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdToolCalls", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('ToolCall', (toolCall) => toolCall.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedToolCalls", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Post', (post) => post.author),
    __metadata("design:type", Array)
], User.prototype, "posts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Post', (post) => post.createdByUser),
    __metadata("design:type", Array)
], User.prototype, "createdPosts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Post', (post) => post.updatedByUser),
    __metadata("design:type", Array)
], User.prototype, "updatedPosts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Account', (account) => account.user),
    __metadata("design:type", Array)
], User.prototype, "accounts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Session', (session) => session.user),
    __metadata("design:type", Array)
], User.prototype, "sessions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('VerificationToken', (verificationToken) => verificationToken.user),
    __metadata("design:type", Array)
], User.prototype, "verificationTokens", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users')
], User);
//# sourceMappingURL=user.entity.js.map