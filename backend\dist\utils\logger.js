"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.log = log;
exports.error = error;
exports.warn = warn;
exports.info = info;
exports.debug = debug;
exports.trace = trace;
exports.table = table;
exports.group = group;
exports.groupCollapsed = groupCollapsed;
exports.groupEnd = groupEnd;
exports.time = time;
exports.timeEnd = timeEnd;
exports.timeLog = timeLog;
exports.count = count;
exports.countReset = countReset;
exports.assert = assert;
exports.clear = clear;
exports.dir = dir;
exports.dirxml = dirxml;
exports.profile = profile;
exports.profileEnd = profileEnd;
exports.timeStamp = timeStamp;
function getCallStackInfo() {
    const stack = new Error().stack;
    if (!stack) {
        return { file: 'unknown', line: 0 };
    }
    const lines = stack.split('\n');
    let callerLine = '';
    for (let i = 2; i < lines.length; i++) {
        const line = lines[i];
        if (line && !line.includes('logger.ts') && !line.includes('getCallStackInfo')) {
            callerLine = line;
            break;
        }
    }
    if (!callerLine) {
        return { file: 'unknown', line: 0 };
    }
    const match = callerLine.match(/at\s+(?:(\S+)\s+\()?([^:]+):(\d+):(\d+)\)?/);
    if (match) {
        const [, functionName, filePath, lineStr, columnStr] = match;
        const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath;
        return {
            file: fileName,
            line: parseInt(lineStr, 10),
            function: functionName || undefined
        };
    }
    return { file: 'unknown', line: 0 };
}
function formatMessage(level, message, context) {
    const { file, line, function: funcName } = context;
    const funcInfo = funcName ? ` (${funcName})` : '';
    return `[${level}] ${file}:${line}${funcInfo} - ${message}`;
}
function log(message, ...args) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('LOG', message, context);
    console.log(formattedMessage, ...args);
}
function error(message, ...args) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('ERROR', message, context);
    console.error(formattedMessage, ...args);
}
function warn(message, ...args) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('WARN', message, context);
    console.warn(formattedMessage, ...args);
}
function info(message, ...args) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('INFO', message, context);
    console.info(formattedMessage, ...args);
}
function debug(message, ...args) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('DEBUG', message, context);
    console.debug(formattedMessage, ...args);
}
function trace(message, ...args) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('TRACE', message, context);
    console.trace(formattedMessage, ...args);
}
function table(data, columns) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('TABLE', 'Table data:', context);
    console.log(formattedMessage);
    console.table(data, columns);
}
function group(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('GROUP', label, context);
    console.group(formattedLabel);
}
function groupCollapsed(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('GROUP', label, context);
    console.groupCollapsed(formattedLabel);
}
function groupEnd() {
    console.groupEnd();
}
function time(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('TIME', label, context);
    console.time(formattedLabel);
}
function timeEnd(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('TIME', label, context);
    console.timeEnd(formattedLabel);
}
function timeLog(label, ...args) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('TIME', label, context);
    console.timeLog(formattedLabel, ...args);
}
function count(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('COUNT', label, context);
    console.count(formattedLabel);
}
function countReset(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('COUNT', label, context);
    console.countReset(formattedLabel);
}
function assert(condition, message, ...args) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('ASSERT', message, context);
    console.assert(condition, formattedMessage, ...args);
}
function clear() {
    console.clear();
}
function dir(obj, options) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('DIR', 'Object inspection:', context);
    console.log(formattedMessage);
    console.dir(obj, options);
}
function dirxml(node) {
    const context = getCallStackInfo();
    const formattedMessage = formatMessage('DIRXML', 'XML/HTML inspection:', context);
    console.log(formattedMessage);
    console.dirxml(node);
}
function profile(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('PROFILE', label, context);
    console.profile(formattedLabel);
}
function profileEnd(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('PROFILE', label, context);
    console.profileEnd(formattedLabel);
}
function timeStamp(label) {
    const context = getCallStackInfo();
    const formattedLabel = formatMessage('TIMESTAMP', label, context);
    console.timeStamp(formattedLabel);
}
const logger = {
    log,
    error,
    warn,
    info,
    debug,
    trace,
    table,
    group,
    groupCollapsed,
    groupEnd,
    time,
    timeEnd,
    timeLog,
    count,
    countReset,
    assert,
    clear,
    dir,
    dirxml,
    profile,
    profileEnd,
    timeStamp
};
exports.default = logger;
//# sourceMappingURL=logger.js.map