"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
let AuthService = class AuthService {
    constructor(usersService, jwtService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
    }
    async validateUser(email, password) {
        return null;
    }
    async login(user) {
        const payload = { email: user.email, sub: user.id };
        console.log('[AuthService] Creating JWT payload:', payload);
        console.log('[AuthService] User object in login:', user);
        const token = this.jwtService.sign(payload);
        console.log('[AuthService] JWT token created, length:', token.length);
        return {
            access_token: token,
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                image: user.image,
            },
        };
    }
    async googleLogin(profile) {
        console.log('[AuthService] Google login with profile:', profile);
        let user = await this.usersService.findByEmail(profile.email);
        console.log('[AuthService] Existing user found:', user);
        if (!user) {
            console.log('[AuthService] Creating new user for Google login');
            user = await this.usersService.create({
                email: profile.email,
                name: profile.name,
                image: profile.picture,
            });
            console.log('[AuthService] New user created:', user);
        }
        else {
            if (user.name !== profile.name || user.image !== profile.picture) {
                console.log('[AuthService] Updating existing user');
                user = await this.usersService.update(user.id, {
                    name: profile.name,
                    image: profile.picture,
                });
                console.log('[AuthService] User updated:', user);
            }
        }
        console.log('[AuthService] Final user object for login:', user);
        return this.login(user);
    }
    async getProfile(userId) {
        console.log('[AuthService] getProfile called with userId:', userId);
        const user = await this.usersService.findOne(userId);
        console.log('[AuthService] getProfile result:', user);
        return user;
    }
    async devLogin(email, name) {
        console.log('[AuthService] Dev login for email:', email);
        let user = await this.usersService.findByEmail(email);
        if (!user) {
            console.log('[AuthService] Creating new user for dev login');
            user = await this.usersService.create({
                email,
                name: name || email.split('@')[0],
                image: null,
            });
        }
        console.log('[AuthService] Dev login successful for user:', user.id);
        return this.login(user);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map