{"version": 3, "file": "conversations.service.js", "sourceRoot": "", "sources": ["../../src/conversations/conversations.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,+DAAqD;AACrD,qDAA2C;AAC3C,wCAA8C;AAGvC,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YAEU,uBAAiD,EAEjD,kBAAuC;QAFvC,4BAAuB,GAAvB,uBAAuB,CAA0B;QAEjD,uBAAkB,GAAlB,kBAAkB,CAAqB;IAC9C,CAAC;IAEJ,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YACvC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;YAC3B,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;YAC9D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;SAChC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAe;QACjC,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YACvC,KAAK,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;YACpC,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YACvC,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;YACnC,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;YAC9D,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,IAAI,YAAY,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,qBAAmI;QAE9I,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,gBAAgB,EAAE,GAAG,qBAAqB,CAAC;QAElF,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE;YAC9C,MAAM;YACN,OAAO;YACP,SAAS;YACT,gBAAgB;YAChB,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE;YAChC,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE;SACnC,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;YACvD,GAAG,gBAAgB;YACnB,IAAI,EAAE,IAAA,oBAAa,GAAE;YACrB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;YAC1B,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;YAC5B,SAAS,EAAE,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,QAAQ,EAAE;SAC7C,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,YAAY,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,qBAA4C;QACnE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,qBAAqB,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC5C,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC;QAC9B,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,EAAU,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QACvE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAG5C,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;YACnE,KAAK,EAAE;gBACL,cAAc,EAAE,EAAE;gBAClB,SAAS,EAAE,KAAK;aACjB;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;YAC3B,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK;YACxB,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QAGH,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACxC,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,cAAc,EAAE,OAAO,CAAC,cAAc;SACvC,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,QAAQ;YACR,KAAK;YACL,IAAI;YACJ,KAAK;YACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACrC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,IAAY,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QAC/E,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAU,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QAGhE,OAAO,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,cAAsB,EAAE,WAcxC;QAEC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAGxD,IAAI,IAAI,GAAG,MAAM,CAAC;QAClB,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,IAAI,GAAG,WAAW,CAAC;QACrB,CAAC;aAAM,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC;YAClC,IAAI,GAAG,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,QAAQ,GAAQ,EAAE,CAAC;QACzB,IAAI,WAAW,CAAC,OAAO;YAAE,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QAChE,IAAI,WAAW,CAAC,UAAU;YAAE,QAAQ,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QACzE,IAAI,WAAW,CAAC,QAAQ;YAAE,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACnE,IAAI,WAAW,CAAC,QAAQ;YAAE,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACnE,IAAI,WAAW,CAAC,aAAa;YAAE,QAAQ,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;QAClF,IAAI,WAAW,CAAC,cAAc;YAAE,QAAQ,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;QACrF,IAAI,WAAW,CAAC,IAAI;YAAE,QAAQ,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QACvD,IAAI,WAAW,CAAC,aAAa;YAAE,QAAQ,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;QAClF,IAAI,WAAW,CAAC,WAAW;YAAE,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC5E,IAAI,WAAW,CAAC,YAAY;YAAE,QAAQ,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;QAG/E,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC7C,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,IAAI;YACJ,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;YAC5D,cAAc;YACd,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,SAAS;YACnD,SAAS,EAAE,WAAW,CAAC,SAAS;SACjC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,WAcpC;QAEC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAGjD,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;IACvD,CAAC;CACF,CAAA;AApNY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;qCADO,oBAAU;QAEf,oBAAU;GAL7B,oBAAoB,CAoNhC"}