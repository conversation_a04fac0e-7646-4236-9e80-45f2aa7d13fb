"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const conversations_service_1 = require("./conversations.service");
let ConversationsController = class ConversationsController {
    constructor(conversationsService) {
        this.conversationsService = conversationsService;
    }
    create(createConversationDto) {
        return this.conversationsService.create(createConversationDto);
    }
    findAll() {
        return this.conversationsService.findAll();
    }
    findByStoreId(storeId) {
        return this.conversationsService.findByStoreId(storeId);
    }
    findByUserId(userId) {
        return this.conversationsService.findByUserId(userId);
    }
    findByUuid(uuid) {
        return this.conversationsService.findByUuid(uuid);
    }
    getUnifiedTimelineByUuid(uuid, page = '1', limit = '50') {
        return this.conversationsService.getUnifiedTimelineByUuid(uuid, parseInt(page, 10), parseInt(limit, 10));
    }
    addMessageByUuid(uuid, messageData) {
        return this.conversationsService.addMessageByUuid(uuid, messageData);
    }
    getTimeline(id, page = '1', limit = '50') {
        return this.conversationsService.getTimeline(id, parseInt(page, 10), parseInt(limit, 10));
    }
    getUnifiedTimeline(id, page = '1', limit = '50') {
        return this.conversationsService.getUnifiedTimeline(id, parseInt(page, 10), parseInt(limit, 10));
    }
    addMessage(id, messageData) {
        const transformedData = {
            content: messageData.content,
            createdBy: messageData.senderId?.toString() || '1',
            userId: messageData.senderId?.toString(),
            ...(messageData.senderType === 'agent' ? { agentId: 'system' } : { customerId: messageData.senderId?.toString() })
        };
        return this.conversationsService.addMessage(id, transformedData);
    }
    findOne(id) {
        return this.conversationsService.findOne(id);
    }
    update(id, updateConversationDto) {
        return this.conversationsService.update(id, updateConversationDto);
    }
    remove(id) {
        return this.conversationsService.remove(id);
    }
};
exports.ConversationsController = ConversationsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new conversation' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Conversation created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all conversations' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversations retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('store/:storeId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get conversations by store ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversations retrieved successfully' }),
    __param(0, (0, common_1.Param)('storeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "findByStoreId", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get conversations by user ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversations retrieved successfully' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('uuid/:uuid'),
    (0, swagger_1.ApiOperation)({ summary: 'Get conversation by UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversation retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Param)('uuid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "findByUuid", null);
__decorate([
    (0, common_1.Get)('uuid/:uuid/unified-timeline'),
    (0, swagger_1.ApiOperation)({ summary: 'Get unified timeline by conversation UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Unified timeline retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Param)('uuid')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "getUnifiedTimelineByUuid", null);
__decorate([
    (0, common_1.Post)('uuid/:uuid/messages'),
    (0, swagger_1.ApiOperation)({ summary: 'Add a message to conversation by UUID' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Message added successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Param)('uuid')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "addMessageByUuid", null);
__decorate([
    (0, common_1.Get)(':id/timeline'),
    (0, swagger_1.ApiOperation)({ summary: 'Get timeline by conversation ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Timeline retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "getTimeline", null);
__decorate([
    (0, common_1.Get)(':id/unified-timeline'),
    (0, swagger_1.ApiOperation)({ summary: 'Get unified timeline by conversation ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Unified timeline retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "getUnifiedTimeline", null);
__decorate([
    (0, common_1.Post)(':id/messages'),
    (0, swagger_1.ApiOperation)({ summary: 'Add a message to conversation by ID' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Message added successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "addMessage", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a conversation by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversation retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a conversation' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversation updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a conversation' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversation deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConversationsController.prototype, "remove", null);
exports.ConversationsController = ConversationsController = __decorate([
    (0, swagger_1.ApiTags)('conversations'),
    (0, common_1.Controller)('conversations'),
    __metadata("design:paramtypes", [conversations_service_1.ConversationsService])
], ConversationsController);
//# sourceMappingURL=conversations.controller.js.map