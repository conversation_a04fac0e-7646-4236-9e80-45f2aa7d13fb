export declare function serializeBigInts<T>(obj: T): T;
export declare function safeJsonSerialize<T>(obj: T): T;
export declare function safeQueryResult<T>(result: T): T;
export declare function safeCountResult(count: any): number;
export declare function serializeStore(store: any): any;
export declare function serializeStores(stores: any[]): any[];
export declare function serializeAgent(agent: any): any;
export declare function serializeAgents(agents: any[]): any[];
export declare function serializeProduct(product: any): any;
export declare function serializeProducts(products: any[]): any[];
export declare function serializeCustomer(customer: any): any;
export declare function serializeCustomers(customers: any[]): any[];
export declare function serializeOrder(order: any): any;
export declare function serializeOrders(orders: any[]): any[];
export declare function serializeConversation(conversation: any): any;
export declare function serializeConversations(conversations: any[]): any[];
