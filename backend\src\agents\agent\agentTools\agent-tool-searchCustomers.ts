import { createTool } from '../../../../../ai-agent/dist';
import { filterCustomers } from '../../services/customer.service';

export async function searchCustomersTool(
	params: { name?: string; email?: string; phone?: string },
	db: any,
	conversationUuid: string
) {
	let { name, email, phone } = params || ({} as any);
	name = typeof name === 'string' ? name.trim() : undefined as any;
	email = typeof email === 'string' ? email.trim() : undefined as any;
	phone = typeof phone === 'string' ? phone.trim() : undefined as any;
	if (!name) name = undefined as any;
	if (!email) email = undefined as any;
	if (!phone) phone = undefined as any;

	// Validate database connection and get repositories
	if (!db || !db.isInitialized) {
		throw new Error('Database connection is not properly initialized');
	}

	const conversationRepo = db.getRepository('Conversation');
	const orderRepo = db.getRepository('Order');

	const convo = await conversationRepo.findOne({
		where: { uuid: conversationUuid, isDeleted: false },
		select: { id: true, storeId: true },
	});
	if (!convo) throw new Error('Conversation not found');

	const { customers } = await filterCustomers(db, 1, 25, {
		storeId: convo.storeId as unknown as bigint,
		email,
		name,
		phone,
	});

	// Save list to context and, if exactly one, set selected customer and fetch recent orders
	try {
		const currentCtx = await conversationRepo.findOne({
			where: { id: convo.id as unknown as bigint },
			select: { context: true },
		});
		const baseCtx = { ...((currentCtx as any)?.context || {}), customers };
		let nextCtx = baseCtx;
		if (Array.isArray(customers) && customers.length === 1) {
			const selected = customers[0] as any;
			const customerOrders = await orderRepo.find({
				where: { isDeleted: false, storeId: convo.storeId, customerId: selected.id as unknown as bigint },
				order: { createdAt: 'asc' },
				relations: { items: true },
				take: 5,
			});
			nextCtx = { ...baseCtx, customer: { id: selected.id, name: selected.name, email: selected.email, phone: selected.phone }, customerOrders };
		}
		await conversationRepo.update(
			{ id: convo.id as unknown as bigint },
			{ context: nextCtx }
		);
	} catch {}

	return {
		count: Array.isArray(customers) ? customers.length : 0,
		customers: (customers || []).map((c: any) => ({ id: c.id, name: c.name, email: c.email, phone: c.phone })),
	};
}

export const searchCustomers = createTool(searchCustomersTool, {
	name: 'searchCustomers',
	description:
		'Search customers by name, email, or phone within the store. Trims inputs and ignores empty fields; combines provided filters. Saves results to context.customers and, when a single match is found, also sets context.customer and their recent orders.',
	parameterTypes: {
		name: { type: 'string', optional: true },
		email: { type: 'string', optional: true },
		phone: { type: 'string', optional: true },
	},
});
