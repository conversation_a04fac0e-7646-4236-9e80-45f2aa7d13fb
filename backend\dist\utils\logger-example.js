"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exampleFunction = exampleFunction;
exports.asyncExample = asyncExample;
exports.conditionalLogging = conditionalLogging;
const logger_1 = require("./logger");
function exampleFunction() {
    (0, logger_1.log)('This is a log message');
    (0, logger_1.error)('This is an error message');
    (0, logger_1.warn)('This is a warning message');
    (0, logger_1.info)('This is an info message');
    (0, logger_1.debug)('This is a debug message');
    (0, logger_1.log)('User data:', { id: 123, name: '<PERSON>' });
    logger_1.default.log('Using default logger object');
    logger_1.default.error('Error with default logger');
    logger_1.default.time('database-query');
    setTimeout(() => {
        logger_1.default.timeEnd('database-query');
    }, 100);
    logger_1.default.group('API Request Details');
    logger_1.default.log('Method: GET');
    logger_1.default.log('URL: /api/users');
    logger_1.default.log('Status: 200');
    logger_1.default.groupEnd();
}
async function asyncExample() {
    try {
        logger_1.default.info('Starting async operation');
        await new Promise(resolve => setTimeout(resolve, 100));
        logger_1.default.info('Async operation completed');
    }
    catch (err) {
        (0, logger_1.error)('Async operation failed:', err);
    }
}
function conditionalLogging(userId) {
    if (userId) {
        logger_1.default.info('User authenticated:', userId);
    }
    else {
        logger_1.default.warn('No user ID provided');
    }
}
if (require.main === module) {
    console.log('=== Logger Example ===\n');
    exampleFunction();
    asyncExample();
    conditionalLogging('user123');
    conditionalLogging();
}
//# sourceMappingURL=logger-example.js.map