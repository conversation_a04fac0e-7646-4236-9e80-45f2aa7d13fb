"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serializeBigInts = serializeBigInts;
exports.safeJsonSerialize = safeJsonSerialize;
exports.safeQueryResult = safeQueryResult;
exports.safeCountResult = safeCountResult;
exports.serializeStore = serializeStore;
exports.serializeStores = serializeStores;
exports.serializeAgent = serializeAgent;
exports.serializeAgents = serializeAgents;
exports.serializeProduct = serializeProduct;
exports.serializeProducts = serializeProducts;
exports.serializeCustomer = serializeCustomer;
exports.serializeCustomers = serializeCustomers;
exports.serializeOrder = serializeOrder;
exports.serializeOrders = serializeOrders;
exports.serializeConversation = serializeConversation;
exports.serializeConversations = serializeConversations;
const bigint_handler_1 = require("./bigint-handler");
function serializeBigInts(obj) {
    return (0, bigint_handler_1.handleTypeORMResult)(obj);
}
function safeJsonSerialize(obj) {
    return serializeBigInts(obj);
}
function safeQueryResult(result) {
    try {
        if (typeof result === 'bigint') {
            return Number(result);
        }
        if (typeof result === 'number') {
            return result;
        }
        return serializeBigInts(result);
    }
    catch (error) {
        console.warn('Error serializing query result:', error);
        return result;
    }
}
function safeCountResult(count) {
    if (typeof count === 'bigint') {
        return Number(count);
    }
    if (typeof count === 'number') {
        return count;
    }
    if (typeof count === 'string') {
        const parsed = parseInt(count, 10);
        return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
}
function serializeStore(store) {
    if (!store)
        return store;
    return {
        ...store,
        id: store.id?.toString(),
        userId: store.userId?.toString(),
        createdBy: store.createdBy?.toString(),
        updatedBy: store.updatedBy?.toString(),
        owner: store.owner ? {
            ...store.owner,
            id: store.owner.id?.toString(),
        } : null,
    };
}
function serializeStores(stores) {
    return stores.map(serializeStore);
}
function serializeAgent(agent) {
    if (!agent)
        return agent;
    return {
        ...agent,
        id: agent.id?.toString(),
        userId: agent.userId?.toString(),
        storeId: agent.storeId?.toString(),
        createdBy: agent.createdBy?.toString(),
        updatedBy: agent.updatedBy?.toString(),
        user: agent.user ? {
            id: agent.user.id?.toString(),
            name: agent.user.name,
            email: agent.user.email,
        } : null,
        store: agent.store ? {
            id: agent.store.id?.toString(),
            name: agent.store.name,
            uuid: agent.store.uuid,
        } : null,
        createdByUser: agent.createdByUser ? {
            id: agent.createdByUser.id?.toString(),
            name: agent.createdByUser.name,
            email: agent.createdByUser.email,
        } : null,
        updatedByUser: agent.updatedByUser ? {
            id: agent.updatedByUser.id?.toString(),
            name: agent.updatedByUser.name,
            email: agent.updatedByUser.email,
        } : null,
    };
}
function serializeAgents(agents) {
    return agents.map(serializeAgent);
}
function serializeProduct(product) {
    if (!product)
        return product;
    return {
        ...product,
        id: product.id?.toString(),
        userId: product.userId?.toString(),
        storeId: product.storeId?.toString(),
        createdBy: product.createdBy?.toString(),
        updatedBy: product.updatedBy?.toString(),
        price: typeof product.price === 'string' ? parseFloat(product.price) : product.price,
        store: product.store ? serializeStore(product.store) : null,
    };
}
function serializeProducts(products) {
    return products.map(serializeProduct);
}
function serializeCustomer(customer) {
    if (!customer)
        return customer;
    return {
        ...customer,
        id: customer.id?.toString(),
        userId: customer.userId?.toString(),
        storeId: customer.storeId?.toString(),
        createdBy: customer.createdBy?.toString(),
        updatedBy: customer.updatedBy?.toString(),
        store: customer.store ? serializeStore(customer.store) : null,
    };
}
function serializeCustomers(customers) {
    return customers.map(serializeCustomer);
}
function serializeOrder(order) {
    if (!order)
        return order;
    return {
        ...order,
        id: order.id?.toString(),
        userId: order.userId?.toString(),
        storeId: order.storeId?.toString(),
        customerId: order.customerId?.toString(),
        createdBy: order.createdBy?.toString(),
        updatedBy: order.updatedBy?.toString(),
        store: order.store ? serializeStore(order.store) : null,
        customer: order.customer ? serializeCustomer(order.customer) : null,
        items: order.items ? order.items.map((item) => ({
            ...item,
            id: item.id?.toString(),
            orderId: item.orderId?.toString(),
            productId: item.productId?.toString(),
            product: item.product ? {
                ...item.product,
                id: item.product.id?.toString(),
            } : null,
        })) : [],
    };
}
function serializeOrders(orders) {
    return orders.map(serializeOrder);
}
function serializeConversation(conversation) {
    if (!conversation)
        return conversation;
    return {
        ...conversation,
        id: conversation.id?.toString(),
        userId: conversation.userId?.toString(),
        storeId: conversation.storeId?.toString(),
        customerId: conversation.customerId?.toString(),
        createdBy: conversation.createdBy?.toString(),
        updatedBy: conversation.updatedBy?.toString(),
        totalCost: conversation.totalCost !== null ? Number(conversation.totalCost) : null,
        totalExecutionTime: conversation.totalExecutionTime !== null ? Number(conversation.totalExecutionTime) : null,
        totalInputTokens: conversation.totalInputTokens !== null ? Number(conversation.totalInputTokens) : null,
        totalOutputTokens: conversation.totalOutputTokens !== null ? Number(conversation.totalOutputTokens) : null,
        store: conversation.store ? serializeStore(conversation.store) : null,
        user: conversation.user ? {
            ...conversation.user,
            id: conversation.user.id?.toString(),
        } : null,
        customer: conversation.customer ? serializeCustomer(conversation.customer) : null,
    };
}
function serializeConversations(conversations) {
    return conversations.map(serializeConversation);
}
//# sourceMappingURL=serialization.js.map