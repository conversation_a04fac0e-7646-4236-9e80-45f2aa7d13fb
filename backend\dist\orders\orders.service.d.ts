import { Repository } from 'typeorm';
import { Order } from './order.entity';
import { OrderItem } from './order-item.entity';
export declare enum OrderStatusService {
    DRAFT = "draft",
    PENDING = "pending",
    CONFIRMED = "confirmed",
    PROCESSING = "processing",
    SHIPPED = "shipped",
    DELIVERED = "delivered",
    CANCELLED = "cancelled",
    RETURNED = "returned"
}
export declare enum OrderPriorityService {
    LOW = "low",
    NORMAL = "normal",
    HIGH = "high",
    URGENT = "urgent"
}
export type CreateOrderItemInput = {
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    taxAmount?: number;
};
export type CreateOrderInput = {
    status?: OrderStatusService;
    priority?: OrderPriorityService;
    useTax?: boolean;
    taxRate?: number;
    orderDate?: Date;
    expectedDeliveryDate?: Date | null;
    preferredDeliveryLocation?: string | null;
    userId: string;
    storeId: string;
    customerId: string;
    customerPhone?: string;
    customerEmail?: string;
    customerName?: string;
    customerAddress?: string;
    createdBy: string;
    items: CreateOrderItemInput[];
};
export type UpdateOrderInput = {
    id: string;
    status?: OrderStatusService;
    priority?: OrderPriorityService;
    useTax?: boolean;
    taxRate?: number;
    orderDate?: Date;
    expectedDeliveryDate?: Date | null;
    preferredDeliveryLocation?: string | null;
    cancellationReason?: string | null;
    updatedBy: string;
    items?: CreateOrderItemInput[];
};
export type OrderFilter = {
    storeId?: string;
    userId?: string;
    customerId?: string;
    status?: OrderStatusService;
    priority?: OrderPriorityService;
};
export declare function computeOrderTotals(items: Array<{
    quantity: number;
    unitPrice: number;
    taxAmount?: number;
}>, useTax: boolean, taxRate: number): {
    subtotal: number;
    taxAmount: number;
    total: number;
};
export declare class OrdersService {
    private orderRepository;
    private orderItemRepository;
    constructor(orderRepository: Repository<Order>, orderItemRepository: Repository<OrderItem>);
    createOrder(input: CreateOrderInput): Promise<Order>;
    updateOrder(input: UpdateOrderInput): Promise<Order>;
    findById(id: string): Promise<Order>;
    findByOrderNumber(orderNumber: string): Promise<Order>;
    findOrders(filter: OrderFilter, page?: number, limit?: number): Promise<{
        orders: Order[];
        total: number;
    }>;
    deleteOrder(id: string, userId: string): Promise<void>;
    getOrderStats(storeId: string, startDate?: Date, endDate?: Date): Promise<{
        totalOrders: number;
        totalRevenue: number;
        averageOrderValue: number;
        ordersByStatus: Record<string, number>;
    }>;
}
