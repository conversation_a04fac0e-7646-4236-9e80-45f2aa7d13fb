"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolCallsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const tool_call_entity_1 = require("./tool-call.entity");
let ToolCallsService = class ToolCallsService {
    constructor(toolCallsRepository) {
        this.toolCallsRepository = toolCallsRepository;
    }
    async findAll() {
        return this.toolCallsRepository.find({
            where: { isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const toolCall = await this.toolCallsRepository.findOne({
            where: { id, isDeleted: false },
        });
        if (!toolCall) {
            throw new common_1.NotFoundException(`ToolCall with ID ${id} not found`);
        }
        return toolCall;
    }
    async findByConversationId(conversationId) {
        return this.toolCallsRepository.find({
            where: { conversationId, isDeleted: false },
            order: { createdAt: 'ASC' },
        });
    }
    async create(createToolCallDto) {
        const { userId, ...toolCallData } = createToolCallDto;
        const toolCall = this.toolCallsRepository.create({
            ...toolCallData,
            createdBy: userId?.toString(),
        });
        return this.toolCallsRepository.save(toolCall);
    }
    async update(id, updateToolCallDto) {
        const toolCall = await this.findOne(id);
        Object.assign(toolCall, updateToolCallDto);
        return this.toolCallsRepository.save(toolCall);
    }
    async remove(id) {
        const toolCall = await this.findOne(id);
        toolCall.isDeleted = true;
        await this.toolCallsRepository.save(toolCall);
    }
};
exports.ToolCallsService = ToolCallsService;
exports.ToolCallsService = ToolCallsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(tool_call_entity_1.ToolCall)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ToolCallsService);
//# sourceMappingURL=tool-calls.service.js.map