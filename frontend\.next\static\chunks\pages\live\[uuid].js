/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/live/[uuid]"],{

/***/ "./node_modules/next/dist/build/polyfills/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/process.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = __webpack_require__.g.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = __webpack_require__.g.process) == null ? void 0 : _global_process1.env) === \"object\" ? __webpack_require__.g.process : __webpack_require__(/*! next/dist/compiled/process */ \"./node_modules/next/dist/compiled/process/browser.js\");\n\n//# sourceMappingURL=process.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3BvbHlmaWxscy9wcm9jZXNzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSxxQ0FBcUMscUJBQU0saUZBQWlGLHFCQUFNLGtFQUFrRSxxQkFBTSxXQUFXLG1CQUFPLENBQUMsd0ZBQTRCOztBQUV6UCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3BvbHlmaWxscy9wcm9jZXNzLmpzP2NhNjUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX2dsb2JhbF9wcm9jZXNzLCBfZ2xvYmFsX3Byb2Nlc3MxO1xubW9kdWxlLmV4cG9ydHMgPSAoKF9nbG9iYWxfcHJvY2VzcyA9IGdsb2JhbC5wcm9jZXNzKSA9PSBudWxsID8gdm9pZCAwIDogX2dsb2JhbF9wcm9jZXNzLmVudikgJiYgdHlwZW9mICgoX2dsb2JhbF9wcm9jZXNzMSA9IGdsb2JhbC5wcm9jZXNzKSA9PSBudWxsID8gdm9pZCAwIDogX2dsb2JhbF9wcm9jZXNzMS5lbnYpID09PSBcIm9iamVjdFwiID8gZ2xvYmFsLnByb2Nlc3MgOiByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL3Byb2Nlc3NcIik7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByb2Nlc3MuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/polyfills/process.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&page=%2Flive%2F%5Buuid%5D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&page=%2Flive%2F%5Buuid%5D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/live/[uuid]\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/live/[uuid].tsx */ \"./src/pages/live/[uuid].tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/live/[uuid]\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNtYWhsbCU1Q0RvY3VtZW50cyU1Q3dvcmtzcGFjZSU1Q3Byb2plY3RzJTVDdGVuby1zdG9yZSU1Q2Zyb250ZW5kJTVDc3JjJTVDcGFnZXMlNUNsaXZlJTVDJTVCdXVpZCU1RC50c3gmcGFnZT0lMkZsaXZlJTJGJTVCdXVpZCU1RCEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxnRUFBNkI7QUFDcEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2FmYTUiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9saXZlL1t1dWlkXVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2xpdmUvW3V1aWRdLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvbGl2ZS9bdXVpZF1cIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&page=%2Flive%2F%5Buuid%5D!\n"));

/***/ }),

/***/ "./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n__webpack_require__(/*! client-only */ \"./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n_c = React__default;\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node =  true && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if ( true && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (false) {}\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || \"object\" === \"undefined\") {\n            var sheet =  true ? this.getSheet() : 0;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (false) {}\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (true) {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {}\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (false) {}\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (false) {}\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if ( true && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    _s();\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\n_s(StyleRegistry, \"vgRS4YV7PcSMQCYHzGaNuBIBcZQ=\");\n_c1 = StyleRegistry;\nfunction useStyleRegistry() {\n    _s1();\n    return React.useContext(StyleSheetContext);\n}\n_s1(useStyleRegistry, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry =  true ? createStyleRegistry() : 0;\nfunction JSXStyle(props) {\n    _s2();\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (false) {}\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\n_s2(JSXStyle, \"48Sqj1BUqkshsPdz6NEWXDn8pF4=\", false, function() {\n    return [\n        useStyleRegistry,\n        useInsertionEffect\n    ];\n});\n_c2 = JSXStyle;\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"React__default\");\n$RefreshReg$(_c1, \"StyleRegistry\");\n$RefreshReg$(_c2, \"JSXStyle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/styled-jsx/dist/index/index.js\n"));

/***/ }),

/***/ "./node_modules/styled-jsx/style.js":
/*!******************************************!*\
  !*** ./node_modules/styled-jsx/style.js ***!
  \******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nmodule.exports = __webpack_require__(/*! ./dist/index */ \"./node_modules/styled-jsx/dist/index/index.js\").style;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvc3R5bGVkLWpzeC9zdHlsZS5qcyIsIm1hcHBpbmdzIjoiO0FBQUFBLCtHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvc3R5bGVkLWpzeC9zdHlsZS5qcz8zNzBiIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2luZGV4Jykuc3R5bGVcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSIsInN0eWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/styled-jsx/style.js\n"));

/***/ }),

/***/ "./src/components/MessageFormatter.tsx":
/*!*********************************************!*\
  !*** ./src/components/MessageFormatter.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\n// Inline spoiler component for Telegram-like spoilers: ||hidden||\nconst Spoiler = (param)=>{\n    let { children, styles } = param;\n    _s();\n    const [revealed, setRevealed] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        onClick: ()=>setRevealed(true),\n        className: revealed ? styles.spoilerReveal : styles.spoiler,\n        role: \"button\",\n        tabIndex: 0,\n        onKeyDown: (e)=>{\n            if (e.key === \"Enter\" || e.key === \" \") setRevealed(true);\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Spoiler, \"KtulkxOojQCTASfIDJjeF1Rg0Bo=\");\n_c = Spoiler;\nfunction renderInline(text, keyPrefix, styles) {\n    var _this = this;\n    const nodes = [];\n    const processText = function(input) {\n        let depth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (depth > 10) return [\n            input\n        ];\n        const result = [];\n        // Images first: ![alt](url)\n        const imageRegex = /(!\\[[^\\]]*\\]\\([^\\)]+\\))/g;\n        const imageParts = input.split(imageRegex);\n        if (imageParts.length > 1) {\n            imageParts.forEach((imgPart, imgIndex)=>{\n                const imgMatch = imgPart.match(/^!\\[([^\\]]*)\\]\\(([^\\)]+)\\)$/);\n                if (imgMatch) {\n                    const [, alt, src] = imgMatch;\n                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: src,\n                        alt: alt,\n                        className: styles.image\n                    }, \"\".concat(keyPrefix, \"-img-\").concat(depth, \"-\").concat(imgIndex), false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 23\n                    }, _this));\n                } else {\n                    result.push(...processText(imgPart, depth + 1));\n                }\n            });\n            return result;\n        }\n        // Markdown links [text](url)\n        const linkRegex = /(\\[[^\\]]+\\]\\([^\\)]+\\))/g;\n        const linkParts = input.split(linkRegex);\n        linkParts.forEach((part, i)=>{\n            const linkMatch = part.match(/^\\[([^\\]]+)\\]\\(([^\\)]+)\\)$/);\n            if (linkMatch) {\n                const [, linkText, href] = linkMatch;\n                result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: href,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: styles.link,\n                    children: linkText\n                }, \"\".concat(keyPrefix, \"-link-\").concat(depth, \"-\").concat(i), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, _this));\n                return;\n            }\n            // Inline code `code`\n            const codeRegex = /(`[^`\\n]+`)/g;\n            const codeParts = part.split(codeRegex);\n            codeParts.forEach((codePart, j)=>{\n                const codeMatch = codePart.match(/^`([^`\\n]+)`$/);\n                if (codeMatch) {\n                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: styles.inlineCode,\n                        children: codeMatch[1]\n                    }, \"\".concat(keyPrefix, \"-code-\").concat(depth, \"-\").concat(i, \"-\").concat(j), false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, _this));\n                    return;\n                }\n                // Strikethrough ~~text~~\n                const strikeRegex = /(~~[^~]+?~~)/g;\n                const strikeParts = codePart.split(strikeRegex);\n                strikeParts.forEach((strikePart, k)=>{\n                    const strikeMatch = strikePart.match(/^~~([^~]+?)~~$/);\n                    if (strikeMatch) {\n                        result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: styles.strikethrough,\n                            children: strikeMatch[1]\n                        }, \"\".concat(keyPrefix, \"-strike-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 15\n                        }, _this));\n                        return;\n                    }\n                    // Underline __text__\n                    const underlineRegex = /(__[^_\\n]+?__)/g;\n                    const underlineParts = strikePart.split(underlineRegex);\n                    underlineParts.forEach((underlinePart, u)=>{\n                        const underlineMatch = underlinePart.match(/^__([^_\\n]+?)__$/);\n                        if (underlineMatch) {\n                            result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: styles.underline,\n                                children: underlineMatch[1]\n                            }, \"\".concat(keyPrefix, \"-underline-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(u), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 17\n                            }, _this));\n                            return;\n                        }\n                        // Bold **text**\n                        const boldRegex = /(\\*\\*[^*\\n]+?\\*\\*)/g;\n                        const boldParts = underlinePart.split(boldRegex);\n                        boldParts.forEach((boldPart, l)=>{\n                            const boldMatch = boldPart.match(/^\\*\\*([^*\\n]+?)\\*\\*$/);\n                            if (boldMatch) {\n                                result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"font-semibold\",\n                                    children: boldMatch[1]\n                                }, \"\".concat(keyPrefix, \"-bold-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 29\n                                }, _this));\n                                return;\n                            }\n                            // Italic *text* or _text_\n                            const italicRegex = /(\\*[^*\\n]+?\\*|_[^_\\n]+?_)/g;\n                            const italicParts = boldPart.split(italicRegex);\n                            italicParts.forEach((italicPart, m)=>{\n                                const italicMatch = italicPart.match(/^\\*([^*\\n]+?)\\*$/) || italicPart.match(/^_([^_\\n]+?)_$/);\n                                if (italicMatch) {\n                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                        className: \"italic opacity-90\",\n                                        children: italicMatch[1]\n                                    }, \"\".concat(keyPrefix, \"-italic-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 31\n                                    }, _this));\n                                    return;\n                                }\n                                // Plain text with autolinks/mentions/hashtags/commands/spoilers and preserved line breaks\n                                if (italicPart) {\n                                    const lines = italicPart.split(\"\\n\");\n                                    lines.forEach((line, n)=>{\n                                        if (n > 0) result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, \"\".concat(keyPrefix, \"-br-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 44\n                                        }, _this));\n                                        if (line) {\n                                            const tokenRegex = RegExp(\"(https?:\\\\/\\\\/[\\\\w.-]+(?:\\\\/[\\\\w\\\\-.~:/?#[\\\\]@!$&'()*+,;=%]*)?|\\\\b[\\\\w.+-]+@[\\\\w.-]+\\\\.[A-Za-z]{2,}\\\\b|\\\\B@[A-Za-z0-9_]{3,}|\\\\B#[A-Za-z0-9_]+|(?<=\\\\s|^)[\\\\/][A-Za-z0-9_]+\\\\b|\\\\|\\\\|[^|\\\\n]+\\\\|\\\\|)\", \"g\");\n                                            const tokens = line.split(tokenRegex);\n                                            tokens.forEach((tok, t)=>{\n                                                if (!tok) return;\n                                                if (/^https?:\\/\\//.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: tok,\n                                                        className: styles.link,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        children: tok\n                                                    }, \"\".concat(keyPrefix, \"-auto-url-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n, \"-\").concat(t), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 39\n                                                    }, _this));\n                                                    return;\n                                                }\n                                                if (/^[\\w.+-]+@[\\w.-]+\\.[A-Za-z]{2,}$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:\".concat(tok),\n                                                        className: styles.link,\n                                                        children: tok\n                                                    }, \"\".concat(keyPrefix, \"-auto-email-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n, \"-\").concat(t), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 39\n                                                    }, _this));\n                                                    return;\n                                                }\n                                                if (/^@[A-Za-z0-9_]{3,}$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: styles.mention,\n                                                        children: tok\n                                                    }, \"\".concat(keyPrefix, \"-mention-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n, \"-\").concat(t), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 39\n                                                    }, _this));\n                                                    return;\n                                                }\n                                                if (/^#[A-Za-z0-9_]+$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: styles.hashtag,\n                                                        children: tok\n                                                    }, \"\".concat(keyPrefix, \"-hashtag-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n, \"-\").concat(t), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 39\n                                                    }, _this));\n                                                    return;\n                                                }\n                                                if (/^\\/[A-Za-z0-9_]+$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: styles.command,\n                                                        children: tok\n                                                    }, \"\".concat(keyPrefix, \"-command-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n, \"-\").concat(t), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 39\n                                                    }, _this));\n                                                    return;\n                                                }\n                                                const spoilerMatch = tok.match(/^\\|\\|([^|\\n]+)\\|\\|$/);\n                                                if (spoilerMatch) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Spoiler, {\n                                                        styles: styles,\n                                                        children: spoilerMatch[1]\n                                                    }, \"\".concat(keyPrefix, \"-spoiler-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n, \"-\").concat(t), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 29\n                                                    }, _this));\n                                                    return;\n                                                }\n                                                result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: tok\n                                                }, \"\".concat(keyPrefix, \"-text-\").concat(depth, \"-\").concat(i, \"-\").concat(j, \"-\").concat(k, \"-\").concat(l, \"-\").concat(m, \"-\").concat(n, \"-\").concat(t), false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 37\n                                                }, _this));\n                                            });\n                                        }\n                                    });\n                                }\n                            });\n                        });\n                    });\n                });\n            });\n        });\n        return result;\n    };\n    return processText(text);\n}\n// Enhanced block rendering with better AI text support\nfunction renderBlocks(text, styles) {\n    const lines = text.split(/\\r?\\n/);\n    const blocks = [];\n    let i = 0;\n    while(i < lines.length){\n        const line = lines[i];\n        // Skip extra blank lines but preserve paragraph separation\n        if (!line.trim()) {\n            i++;\n            continue;\n        }\n        const trimmed = line.trim();\n        // Horizontal rule --- or ***\n        if (/^(-{3,}|\\*{3,}|_{3,})$/.test(trimmed)) {\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                className: styles.hr\n            }, \"hr-\".concat(i), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 223,\n                columnNumber: 19\n            }, this));\n            i++;\n            continue;\n        }\n        // Code block with language detection ```lang\n        if (/^```/.test(trimmed)) {\n            const langMatch = trimmed.match(/^```(\\w+)?/);\n            const language = (langMatch === null || langMatch === void 0 ? void 0 : langMatch[1]) || \"\";\n            const codeLines = [];\n            i++;\n            while(i < lines.length && !/^```/.test(lines[i].trim())){\n                codeLines.push(lines[i]);\n                i++;\n            }\n            // Skip closing ``` if present\n            if (i < lines.length && /^```/.test(lines[i].trim())) i++;\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: styles.codeBlockWrapper,\n                children: [\n                    language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: styles.codeBlockLanguage,\n                        children: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: styles.codeBlockPre,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            children: codeLines.join(\"\\n\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"code-\".concat(i), true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Image-only line\n        const imageLineMatch = trimmed.match(/^!\\[([^\\]]*)\\]\\(([^\\)]+)\\)$/);\n        if (imageLineMatch) {\n            const [, alt, src] = imageLineMatch;\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: src,\n                    alt: alt,\n                    className: styles.image\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, this)\n            }, \"img-\".concat(i), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this));\n            i++;\n            continue;\n        }\n        // GitHub-style tables\n        if (/^\\|?.*\\|.*$/.test(trimmed) && i + 1 < lines.length && /^\\|?\\s*:?[-]{3,}.*\\|.*$/.test(lines[i + 1].trim())) {\n            const headerCells = trimmed.replace(/^\\||\\|$/g, \"\").split(\"|\").map((c)=>c.trim());\n            i += 2; // skip separator\n            const bodyRows = [];\n            while(i < lines.length && /\\|/.test(lines[i])){\n                const rowCells = lines[i].replace(/^\\||\\|$/g, \"\").split(\"|\").map((c)=>c.trim());\n                bodyRows.push(rowCells);\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: styles.table,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: headerCells.map((cell, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: styles.tableHeader,\n                                    children: cell\n                                }, \"th-\".concat(idx), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: bodyRows.map((row, r)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: row.map((cell, c)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: styles.tableCell,\n                                        children: cell\n                                    }, \"td-\".concat(r, \"-\").concat(c), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 19\n                                    }, this))\n                            }, \"tr-\".concat(r), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"table-\".concat(i), true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Blockquote > text\n        if (/^>\\s*/.test(trimmed)) {\n            const quoteLines = [];\n            while(i < lines.length && /^>\\s*/.test(lines[i].trim())){\n                const quoteLine = lines[i].replace(/^>\\s*/, \"\");\n                quoteLines.push(quoteLine);\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                className: styles.blockquote,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: styles.blockquoteBar\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: renderBlocks(quoteLines.join(\"\\n\"), styles)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"quote-\".concat(i), true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Headings #, ##, ###, ####, #####, ######\n        const heading = trimmed.match(/^(#{1,6})\\s+(.*)$/);\n        if (heading) {\n            const level = heading[1].length;\n            const content = heading[2];\n            const Tag = \"h\".concat(Math.min(level, 6));\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                className: \"\".concat(styles.heading, \" \").concat(getHeadingSize(level)),\n                children: renderInline(content, \"h-\".concat(i), styles)\n            }, \"h-\".concat(i), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this));\n            i++;\n            continue;\n        }\n        // Enhanced ordered list with nested support\n        const orderedItem = trimmed.match(/^(\\s*)(\\d+)\\.\\s+(.*)$/);\n        if (orderedItem) {\n            const baseIndent = orderedItem[1].length;\n            const items = [];\n            while(i < lines.length){\n                const li = lines[i];\n                const liTrimmed = li.trim();\n                const m = li.match(/^(\\s*)(\\d+)\\.\\s+(.*)$/);\n                if (!m || !liTrimmed) break;\n                const indent = m[1].length;\n                if (indent < baseIndent) break; // Less indented = end of this list\n                const content = m[3];\n                items.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: styles.olLi,\n                    children: renderInline(content, \"ol-li-\".concat(i), styles)\n                }, \"ol-li-\".concat(i), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 11\n                }, this));\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                className: \"mb-3 pl-6 space-y-1 list-decimal\",\n                children: items\n            }, \"ol-\".concat(i), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 364,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Enhanced unordered list with nested support\n        const unorderedItem = trimmed.match(/^(\\s*)[-*+]\\s+(.*)$/);\n        if (unorderedItem) {\n            const baseIndent = unorderedItem[1].length;\n            const items = [];\n            while(i < lines.length){\n                const li = lines[i];\n                const liTrimmed = li.trim();\n                const m = li.match(/^(\\s*)[-*+]\\s+(.*)$/);\n                if (!m || !liTrimmed) break;\n                const indent = m[1].length;\n                if (indent < baseIndent) break; // Less indented = end of this list\n                const content = m[2];\n                items.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: styles.ulLi,\n                    children: renderInline(content, \"ul-li-\".concat(i), styles)\n                }, \"ul-li-\".concat(i), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 11\n                }, this));\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"mb-3 pl-6 space-y-1 list-disc\",\n                children: items\n            }, \"ul-\".concat(i), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Task list [ ] and [x]\n        const taskItem = trimmed.match(/^(\\s*)[-*+]\\s+\\[([ x])\\]\\s+(.*)$/i);\n        if (taskItem) {\n            const baseIndent = taskItem[1].length;\n            const items = [];\n            while(i < lines.length){\n                const li = lines[i];\n                const liTrimmed = li.trim();\n                const m = li.match(/^(\\s*)[-*+]\\s+\\[([ x])\\]\\s+(.*)$/i);\n                if (!m || !liTrimmed) break;\n                const indent = m[1].length;\n                if (indent < baseIndent) break;\n                const checked = m[2].toLowerCase() === \"x\";\n                const content = m[3];\n                items.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"\".concat(styles.ulLi, \" flex items-start gap-2\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            checked: checked,\n                            readOnly: true,\n                            className: \"mt-1 pointer-events-none\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: checked ? \"line-through opacity-60\" : \"\",\n                            children: renderInline(content, \"task-li-\".concat(i), styles)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, \"task-li-\".concat(i), true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 11\n                }, this));\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"mb-3 pl-6 space-y-1 list-none\",\n                children: items\n            }, \"task-\".concat(i), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 440,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Paragraph - collect consecutive non-empty, non-special lines\n        const para = [\n            line\n        ];\n        i++;\n        while(i < lines.length && lines[i].trim() && !/^```/.test(lines[i].trim()) && !/^#{1,6}\\s+/.test(lines[i].trim()) && !/^(\\s*)\\d+\\.\\s+/.test(lines[i]) && !/^(\\s*)[-*+]\\s+/.test(lines[i]) && !/^>\\s*/.test(lines[i].trim()) && !/^(-{3,}|\\*{3,}|_{3,})$/.test(lines[i].trim())){\n            para.push(lines[i]);\n            i++;\n        }\n        blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: styles.paragraph,\n            children: renderInline(para.join(\"\\n\"), \"p-\".concat(i), styles)\n        }, \"p-\".concat(i), false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n            lineNumber: 463,\n            columnNumber: 7\n        }, this));\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: blocks\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n        lineNumber: 469,\n        columnNumber: 10\n    }, this);\n}\n// Helper function for heading sizes\nfunction getHeadingSize(level) {\n    const sizes = {\n        1: \"text-2xl\",\n        2: \"text-xl\",\n        3: \"text-lg\",\n        4: \"text-base\",\n        5: \"text-sm\",\n        6: \"text-xs\"\n    };\n    return sizes[level] || \"text-base\";\n}\nconst MessageFormatter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = (param)=>{\n    let { content, variant = \"dark\" } = param;\n    const styles = variant === \"light\" ? {\n        link: \"text-indigo-600 hover:text-indigo-800 underline transition-colors\",\n        inlineCode: \"bg-gray-100 text-emerald-700 px-1.5 py-0.5 rounded text-[0.85em] font-mono border\",\n        heading: \"text-gray-900 font-semibold mt-4 mb-3 border-b border-gray-200 pb-1\",\n        olLi: \"text-gray-800 text-sm leading-relaxed mb-1\",\n        ulLi: \"text-gray-800 text-sm leading-relaxed mb-1\",\n        paragraph: \"mb-4 text-gray-800 text-sm leading-relaxed\",\n        codeBlockWrapper: \"my-4 bg-gray-50 border border-gray-200 rounded-lg font-mono text-sm overflow-hidden\",\n        codeBlockPre: \"text-gray-800 p-4 overflow-x-auto whitespace-pre\",\n        codeBlockLanguage: \"bg-gray-100 text-gray-600 px-3 py-1 text-xs font-medium border-b border-gray-200\",\n        blockquote: \"my-4 flex gap-3 p-3 bg-gray-50 border-l-4 border-indigo-400 rounded-r-lg\",\n        blockquoteBar: \"w-1 bg-indigo-400 rounded-full flex-shrink-0\",\n        strikethrough: \"line-through opacity-60\",\n        table: \"my-4 border-collapse border border-gray-300 rounded-lg overflow-hidden\",\n        tableHeader: \"bg-gray-100 border border-gray-300 px-3 py-2 font-medium text-gray-900\",\n        tableCell: \"border border-gray-300 px-3 py-2 text-gray-800\",\n        hr: \"my-6 border-0 h-px bg-gray-300\",\n        mention: \"text-blue-700 bg-blue-50 px-1 rounded\",\n        hashtag: \"text-purple-700 bg-purple-50 px-1 rounded\",\n        command: \"text-emerald-700 bg-emerald-50 px-1 rounded font-medium\",\n        underline: \"underline underline-offset-2\",\n        spoiler: \"bg-gray-300 text-gray-300 rounded px-1 cursor-pointer select-none\",\n        spoilerReveal: \"bg-gray-100 text-gray-800 rounded px-1\",\n        image: \"max-w-full h-auto rounded border border-gray-200\"\n    } : {\n        link: \"text-blue-400 hover:text-blue-300 underline transition-colors\",\n        inlineCode: \"bg-slate-700/50 text-green-300 px-1.5 py-0.5 rounded text-[0.85em] font-mono border border-slate-600/30\",\n        heading: \"text-slate-100 font-semibold mt-4 mb-3 border-b border-slate-600/40 pb-1\",\n        olLi: \"text-slate-200 text-sm leading-relaxed mb-1\",\n        ulLi: \"text-slate-200 text-sm leading-relaxed mb-1\",\n        paragraph: \"mb-4 text-slate-200 text-sm leading-relaxed\",\n        codeBlockWrapper: \"my-4 bg-slate-800/60 border border-slate-600/40 rounded-lg font-mono text-sm overflow-hidden\",\n        codeBlockPre: \"text-green-300 p-4 overflow-x-auto whitespace-pre\",\n        codeBlockLanguage: \"bg-slate-700/50 text-slate-300 px-3 py-1 text-xs font-medium border-b border-slate-600/40\",\n        blockquote: \"my-4 flex gap-3 p-3 bg-slate-800/40 border-l-4 border-blue-400 rounded-r-lg\",\n        blockquoteBar: \"w-1 bg-blue-400 rounded-full flex-shrink-0\",\n        strikethrough: \"line-through opacity-60\",\n        table: \"my-4 border-collapse border border-slate-600 rounded-lg overflow-hidden\",\n        tableHeader: \"bg-slate-700 border border-slate-600 px-3 py-2 font-medium text-slate-200\",\n        tableCell: \"border border-slate-600 px-3 py-2 text-slate-300\",\n        hr: \"my-6 border-0 h-px bg-slate-600\",\n        mention: \"text-blue-300 bg-slate-700/50 px-1 rounded\",\n        hashtag: \"text-fuchsia-300 bg-slate-700/50 px-1 rounded\",\n        command: \"text-emerald-300 bg-slate-700/50 px-1 rounded font-medium\",\n        underline: \"underline underline-offset-2 decoration-slate-400\",\n        spoiler: \"bg-slate-500 text-slate-500 rounded px-1 cursor-pointer select-none\",\n        spoilerReveal: \"bg-slate-800 text-slate-100 rounded px-1\",\n        image: \"max-w-full h-auto rounded border border-slate-600/40\"\n    };\n    const formatMessage = (text)=>{\n        try {\n            // Preprocess text to normalize line endings and handle edge cases\n            const normalizedText = text.replace(/\\r\\n/g, \"\\n\") // Normalize line endings\n            .replace(/\\r/g, \"\\n\") // Handle old Mac line endings\n            .trim(); // Remove leading/trailing whitespace\n            if (!normalizedText) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 italic\",\n                    children: \"Empty message\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 16\n                }, undefined);\n            }\n            return renderBlocks(normalizedText, styles);\n        } catch (error) {\n            console.error(\"MessageFormatter error:\", error);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"whitespace-pre-wrap text-sm leading-relaxed \".concat(variant === \"light\" ? \"text-gray-800\" : \"text-slate-200\"),\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 553,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"message-formatter \".concat(variant === \"light\" ? \"prose prose-sm max-w-none\" : \"prose prose-sm prose-invert max-w-none\"),\n        children: formatMessage(content)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n        lineNumber: 563,\n        columnNumber: 5\n    }, undefined);\n});\n_c2 = MessageFormatter;\nMessageFormatter.displayName = \"MessageFormatter\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MessageFormatter);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Spoiler\");\n$RefreshReg$(_c1, \"MessageFormatter$memo\");\n$RefreshReg$(_c2, \"MessageFormatter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MessageFormatter.tsx\n"));

/***/ }),

/***/ "./src/components/TimelineItem.tsx":
/*!*****************************************!*\
  !*** ./src/components/TimelineItem.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TimelineItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageFormatter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageFormatter */ \"./src/components/MessageFormatter.tsx\");\n\n\n\n// Icon Components\nconst UserIcon = (param)=>{\n    let { name } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-blue-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined);\n};\n_c = UserIcon;\nconst AIAgentIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-emerald-500 to-cyan-500 flex items-center justify-center text-white shadow-lg border border-emerald-400/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:w-5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n            lineNumber: 65,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 64,\n        columnNumber: 3\n    }, undefined);\n_c1 = AIAgentIcon;\nconst CustomerIcon = (param)=>{\n    let { name } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 72,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = CustomerIcon;\nconst ToolIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-amber-500 to-yellow-500 flex items-center justify-center text-white shadow-lg border border-amber-400/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:w-5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n            lineNumber: 79,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, undefined);\n_c3 = ToolIcon;\n// Performance Metrics Component\nconst PerformanceMetrics = (param)=>{\n    let { cost, executionTime, inputTokens, outputTokens, variant = \"default\" } = param;\n    // Debug logging to help identify type mismatches\n    if (cost !== null && typeof cost !== \"number\") {\n        console.warn(\"PerformanceMetrics: cost is not a number:\", cost, typeof cost, \"Converting to number...\");\n    }\n    if (executionTime !== null && typeof executionTime !== \"number\") {\n        console.warn(\"PerformanceMetrics: executionTime is not a number:\", executionTime, typeof executionTime, \"Converting to number...\");\n    }\n    if (inputTokens !== null && typeof inputTokens !== \"number\") {\n        console.warn(\"PerformanceMetrics: inputTokens is not a number:\", inputTokens, typeof inputTokens, \"Converting to number...\");\n    }\n    if (outputTokens !== null && typeof outputTokens !== \"number\") {\n        console.warn(\"PerformanceMetrics: outputTokens is not a number:\", outputTokens, typeof outputTokens, \"Converting to number...\");\n    }\n    const hasMetrics = cost !== null || executionTime !== null || inputTokens !== null || outputTokens !== null;\n    if (!hasMetrics) return null;\n    const baseClasses = variant === \"tool\" ? \"bg-amber-500/10 border border-amber-400/20 rounded-md p-2 mb-2\" : \"mt-3 pt-3 border-t border-current/20\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: baseClasses,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs font-medium opacity-80 mb-2\",\n                children: \"Performance Metrics:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-2 text-xs\",\n                children: [\n                    cost !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"\\uD83D\\uDCB0 Cost: \",\n                            typeof cost === \"number\" ? \"$\".concat(cost.toFixed(6)) : \"$\".concat(Number(cost) || 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined),\n                    executionTime !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"⏱️ Time: \",\n                            typeof executionTime === \"number\" ? \"\".concat(executionTime, \"ms\") : \"\".concat(Number(executionTime) || 0, \"ms\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, undefined),\n                    inputTokens !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"\\uD83D\\uDCE5 Input: \",\n                            typeof inputTokens === \"number\" ? \"\".concat(inputTokens, \" tokens\") : \"\".concat(Number(inputTokens) || 0, \" tokens\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined),\n                    outputTokens !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"\\uD83D\\uDCE4 Output: \",\n                            typeof outputTokens === \"number\" ? \"\".concat(outputTokens, \" tokens\") : \"\".concat(Number(outputTokens) || 0, \" tokens\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n_c4 = PerformanceMetrics;\nfunction TimelineItem(param) {\n    let { item } = param;\n    const timestamp = new Date(item.createdAt);\n    if (item.type === \"message\") {\n        var _item_customer, _item_customer1, _item_user, _item_user1;\n        // Check if this is a customer message by looking at the content format\n        const isCustomerMessage = item.content && item.content.startsWith(\"[\") && item.content.includes(\"]:\");\n        const isAgent = !item.user && !item.customer && !isCustomerMessage;\n        const isCustomer = !!item.customer || isCustomerMessage;\n        const isUser = !!item.user;\n        // Extract customer name from message content if it's a customer message\n        let senderName = \"Unknown\";\n        if (isCustomerMessage) {\n            const match = item.content.match(/^\\[([^\\]]+)\\]:/);\n            senderName = match ? match[1] : \"Customer\";\n        } else if (((_item_customer = item.customer) === null || _item_customer === void 0 ? void 0 : _item_customer.name) || ((_item_customer1 = item.customer) === null || _item_customer1 === void 0 ? void 0 : _item_customer1.email)) {\n            senderName = item.customer.name || item.customer.email || \"Customer\";\n        } else if (isAgent) {\n            senderName = \"AI Agent\";\n        } else if (((_item_user = item.user) === null || _item_user === void 0 ? void 0 : _item_user.name) || ((_item_user1 = item.user) === null || _item_user1 === void 0 ? void 0 : _item_user1.email)) {\n            senderName = item.user.name || item.user.email || \"User\";\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3 md:gap-4 \".concat(isCustomer ? \"justify-end\" : \"justify-start\"),\n            children: [\n                !isCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 mt-1\",\n                    children: isAgent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIAgentIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserIcon, {\n                        name: senderName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[85%] md:max-w-[70%] \".concat(isCustomer ? \"order-1\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg \".concat(isAgent ? \"bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\" : isCustomer ? \"bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-400/30 text-orange-100\" : \"bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-400/30 text-blue-100\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between gap-2 md:gap-4 mb-1 md:mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-xs md:text-sm opacity-90\",\n                                        style: {\n                                            fontFamily: \"Exo 2, sans-serif\"\n                                        },\n                                        children: senderName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs opacity-60\",\n                                        style: {\n                                            fontFamily: \"Exo 2, sans-serif\"\n                                        },\n                                        children: isNaN(timestamp.getTime()) ? \"\" : timestamp.toLocaleTimeString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontFamily: \"Exo 2, sans-serif\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageFormatter__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    content: isCustomerMessage ? item.content.replace(/^\\[[^\\]]+\\]:\\s*/, \"\") : item.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PerformanceMetrics, {\n                                cost: item.cost,\n                                executionTime: item.executionTime,\n                                inputTokens: item.inputTokens,\n                                outputTokens: item.outputTokens\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            (item.imageUrl || item.videoUrl || item.attachmentUrl) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 pt-3 border-t border-current/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium opacity-80 mb-2\",\n                                        children: \"Attachments:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            item.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-80\",\n                                                children: [\n                                                    \"\\uD83D\\uDCF7 Image: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.imageUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"underline hover:opacity-100\",\n                                                        children: item.imageUrl\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-80\",\n                                                children: [\n                                                    \"\\uD83C\\uDFA5 Video: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.videoUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"underline hover:opacity-100\",\n                                                        children: item.videoUrl\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.attachmentUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-80\",\n                                                children: [\n                                                    \"\\uD83D\\uDCCE \",\n                                                    item.attachmentType || \"File\",\n                                                    \": \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.attachmentUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"underline hover:opacity-100\",\n                                                        children: item.attachmentUrl\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 59\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                isCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 mt-1 order-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                        name: senderName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    // Tool call item\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 md:gap-4 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolIcon, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[85%] md:max-w-[70%]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-amber-500/20 to-yellow-500/20 border border-amber-400/30 text-amber-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between gap-2 md:gap-4 mb-1 md:mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-xs md:text-sm opacity-90\",\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    children: [\n                                        \"\\uD83D\\uDD27 \",\n                                        item.toolName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-60\",\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    children: isNaN(timestamp.getTime()) ? \"\" : timestamp.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(item.success ? \"bg-emerald-500/10 text-emerald-300 ring-1 ring-emerald-500/20\" : \"bg-red-500/10 text-red-300 ring-1 ring-red-500/20\"),\n                                            children: item.success ? \"✅ Success\" : \"❌ Failed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this),\n                                        item.executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-amber-300/80\",\n                                            children: [\n                                                \"⏱️ \",\n                                                item.executionTime,\n                                                \"ms\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PerformanceMetrics, {\n                                    cost: item.cost,\n                                    executionTime: item.executionTime,\n                                    inputTokens: item.inputTokens,\n                                    outputTokens: item.outputTokens,\n                                    variant: \"tool\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                item.toolInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium opacity-80 mb-1\",\n                                            children: \"Input:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-amber-500/10 border border-amber-400/20 rounded-md p-2 text-xs overflow-x-auto\",\n                                            children: JSON.stringify(item.toolInput, null, 2)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this),\n                                item.success && item.toolOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium opacity-80 mb-1\",\n                                            children: \"Output:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-emerald-500/10 border border-emerald-500/20 rounded-md p-2 text-xs overflow-x-auto\",\n                                            children: JSON.stringify(item.toolOutput, null, 2)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                !item.success && item.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium opacity-80 mb-1 text-red-300\",\n                                            children: \"Error:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-500/10 border border-red-500/20 rounded-md p-2 text-xs text-red-200\",\n                                            children: item.errorMessage\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n_c5 = TimelineItem;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"UserIcon\");\n$RefreshReg$(_c1, \"AIAgentIcon\");\n$RefreshReg$(_c2, \"CustomerIcon\");\n$RefreshReg$(_c3, \"ToolIcon\");\n$RefreshReg$(_c4, \"PerformanceMetrics\");\n$RefreshReg$(_c5, \"TimelineItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/TimelineItem.tsx\n"));

/***/ }),

/***/ "./src/pages/live/[uuid].tsx":
/*!***********************************!*\
  !*** ./src/pages/live/[uuid].tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PublicLiveConversationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/TimelineItem */ \"./src/components/TimelineItem.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Icon Components\nconst CustomerIcon = (param)=>{\n    let { name } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CustomerIcon;\nfunction PublicLiveConversationPage() {\n    var _timeline_timeline, _conversation_store;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { uuid } = router.query;\n    const [page] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isCustomerNameSet, setIsCustomerNameSet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const limit = 50;\n    const conversationUuid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        // Wait for router to be ready and uuid to be available\n        if (router.isReady && uuid) {\n            return uuid;\n        }\n        return null;\n    }, [\n        router.isReady,\n        uuid\n    ]);\n    // Fetch conversation data\n    const fetchConversation = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getByUuid(conversationUuid);\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setConversation(result.data || result);\n            } else {\n                setConversation(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching conversation:\", err);\n            setError(\"Failed to load conversation\");\n        }\n    };\n    // Fetch timeline data\n    const fetchTimeline = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getUnifiedTimelineByUuid(conversationUuid, {\n                page,\n                limit\n            });\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setTimeline(result.data || result);\n            } else {\n                setTimeline(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching timeline:\", err);\n            setError(\"Failed to load timeline\");\n        }\n    };\n    // Fetch data when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (conversationUuid && router.isReady) {\n            fetchConversation();\n            fetchTimeline();\n        }\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    // Set up polling for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!conversationUuid || !router.isReady) return;\n        const interval = setInterval(()=>{\n            fetchConversation();\n            fetchTimeline();\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    const appendMessage = async (messageData)=>{\n        if (!conversationUuid) return;\n        try {\n            var _messagesEndRef_current;\n            console.log(\"\\uD83D\\uDD27 Frontend: Sending message:\", messageData);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.appendMessageByUuid(conversationUuid, messageData);\n            console.log(\"✅ Frontend: Message sent successfully:\", response);\n            setNewMessage(\"\");\n            // Refresh data\n            fetchConversation();\n            fetchTimeline();\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        } catch (err) {\n            console.error(\"❌ Frontend: Failed to send message:\", err);\n            setError(\"Failed to send message\");\n        }\n    };\n    // Use a ref to track previous message count to prevent unnecessary effects\n    const prevMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        var _timeline_timeline;\n        const currentMessageCount = (timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length) || 0;\n        if (currentMessageCount > prevMessageCountRef.current) {\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n            prevMessageCountRef.current = currentMessageCount;\n        }\n    }, [\n        timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage(e);\n        }\n    };\n    const handleSendMessage = (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || !conversation) return;\n        // Additional validation\n        if (!conversation.uuid) {\n            console.error(\"Conversation UUID is missing\");\n            return;\n        }\n        // Require customer name\n        if (!customerName.trim()) {\n            setError(\"Please enter your name to send a message\");\n            return;\n        }\n        try {\n            // Create customer message\n            const messageData = {\n                content: \"[\".concat(customerName.trim(), \"]: \").concat(newMessage.trim()),\n                createdBy: \"1\",\n                agentId: \"customer-message\"\n            };\n            appendMessage(messageData);\n        } catch (error) {\n            console.error(\"Failed to prepare message:\", error);\n        }\n    };\n    // Customer name setup modal\n    if (!isCustomerNameSet && conversation) {\n        var _conversation_store1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-slate-950 relative overflow-hidden flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Join Live Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Join the live customer conversation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: \"\\n              radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n            \"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 max-w-md mx-auto px-6 py-8 bg-slate-900/50 border border-cyan-500/20 rounded-xl backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400 mb-2\",\n                                    children: \"Join Live Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-cyan-300/80\",\n                                    children: [\n                                        (conversation === null || conversation === void 0 ? void 0 : (_conversation_store1 = conversation.store) === null || _conversation_store1 === void 0 ? void 0 : _conversation_store1.name) || \"Store\",\n                                        \" - Live Conversation\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                if (customerName.trim()) {\n                                    setIsCustomerNameSet(true);\n                                }\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-cyan-300 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            placeholder: \"Enter your name\",\n                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 text-cyan-100 placeholder-cyan-400/50\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !customerName.trim(),\n                                    className: \"w-full px-6 py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95\",\n                                    children: \"Join Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            fontFamily: 'system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n        },\n        className: \"jsx-6493e67e825933d4\" + \" \" + \"min-h-screen bg-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-6493e67e825933d4\",\n                        children: (conversation === null || conversation === void 0 ? void 0 : conversation.title) || \"Live Chat\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Live customer conversation\",\n                        className: \"jsx-6493e67e825933d4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-6493e67e825933d4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-6493e67e825933d4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-6493e67e825933d4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-6493e67e825933d4\" + \" \" + \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6493e67e825933d4\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n          \"\n                        },\n                        className: \"jsx-6493e67e825933d4\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%),\\n            linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%)\\n          \",\n                            backgroundSize: \"50px 50px\"\n                        },\n                        className: \"jsx-6493e67e825933d4\" + \" \" + \"absolute inset-0 opacity-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-6493e67e825933d4\" + \" \" + \"relative z-10 max-w-4xl mx-auto px-4 py-4 md:py-6 h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6493e67e825933d4\" + \" \" + \"mb-4 md:mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontFamily: \"Orbitron, monospace\"\n                                },\n                                className: \"jsx-6493e67e825933d4\" + \" \" + \"text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400\",\n                                children: (conversation === null || conversation === void 0 ? void 0 : (_conversation_store = conversation.store) === null || _conversation_store === void 0 ? void 0 : _conversation_store.name) || \"LIVE CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontFamily: \"Exo 2, sans-serif\"\n                                },\n                                className: \"jsx-6493e67e825933d4\" + \" \" + \"text-sm md:text-base text-cyan-300/80 mt-1\",\n                                children: [\n                                    \"Live Chat - \",\n                                    customerName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6493e67e825933d4\" + \" \" + \"bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg backdrop-blur-sm mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-6493e67e825933d4\" + \" \" + \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    className: \"jsx-6493e67e825933d4\" + \" \" + \"w-5 h-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\",\n                                        className: \"jsx-6493e67e825933d4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6493e67e825933d4\" + \" \" + \"flex-1 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-6493e67e825933d4\" + \" \" + \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-6493e67e825933d4\" + \" \" + \"relative w-16 h-16 mx-auto mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-6493e67e825933d4\" + \" \" + \"absolute inset-0 border-4 border-cyan-400/20 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-6493e67e825933d4\" + \" \" + \"absolute inset-0 border-4 border-transparent border-t-cyan-400 rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    className: \"jsx-6493e67e825933d4\" + \" \" + \"text-cyan-300/80\",\n                                    children: \"Initializing Neural Interface...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    conversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6493e67e825933d4\" + \" \" + \"flex-1 flex flex-col bg-slate-900/30 border border-cyan-500/20 rounded-xl backdrop-blur-sm overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-6493e67e825933d4\" + \" \" + \"flex-1 overflow-y-auto p-3 md:p-4 space-y-3 md:space-y-4 scrollbar-thin scrollbar-thumb-cyan-500/30 scrollbar-track-transparent\",\n                                children: [\n                                    ((timeline === null || timeline === void 0 ? void 0 : timeline.timeline) || []).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            item: item\n                                        }, \"\".concat(item.type, \"-\").concat(item.id), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.notificationStatus) && conversation.notificationStatus !== \"None\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-6493e67e825933d4\" + \" \" + \"flex gap-3 md:gap-4 justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-6493e67e825933d4\" + \" \" + \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                                                    name: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-6493e67e825933d4\" + \" \" + \"max-w-[85%] md:max-w-[70%]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-6493e67e825933d4\" + \" \" + \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-6493e67e825933d4\" + \" \" + \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontFamily: \"Exo 2, sans-serif\"\n                                                                },\n                                                                className: \"jsx-6493e67e825933d4\" + \" \" + \"text-xs opacity-80\",\n                                                                children: conversation.notificationStatus === \"AgentIsThinking\" ? \"AI Agent is thinking...\" : \"AI Agent is generating response...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-6493e67e825933d4\" + \" \" + \"inline-flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-6493e67e825933d4\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-6493e67e825933d4\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-6493e67e825933d4\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-6493e67e825933d4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSendMessage,\n                                className: \"jsx-6493e67e825933d4\" + \" \" + \"border-t border-cyan-500/20 p-3 md:p-4 bg-slate-900/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-6493e67e825933d4\" + \" \" + \"flex gap-2 md:gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-6493e67e825933d4\" + \" \" + \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Enter your message...\",\n                                                    style: {\n                                                        fontFamily: \"Exo 2, sans-serif\"\n                                                    },\n                                                    rows: 1,\n                                                    disabled: isLoading,\n                                                    className: \"jsx-6493e67e825933d4\" + \" \" + \"w-full px-3 md:px-4 py-2 md:py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 resize-none text-cyan-100 placeholder-cyan-400/50 text-sm md:text-base transition-all duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-6493e67e825933d4\" + \" \" + \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-6493e67e825933d4\" + \" \" + \"w-4 h-4 border-2 border-cyan-400/30 border-t-cyan-400 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: !newMessage.trim() || isLoading || !customerName.trim(),\n                                            style: {\n                                                fontFamily: \"Exo 2, sans-serif\"\n                                            },\n                                            className: \"jsx-6493e67e825933d4\" + \" \" + \"px-4 md:px-6 py-2 md:py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-cyan-500/25 text-sm md:text-base\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-6493e67e825933d4\" + \" \" + \"w-4 h-4 md:w-5 md:h-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\",\n                                                    className: \"jsx-6493e67e825933d4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"6493e67e825933d4\",\n                children: \".scrollbar-thin{scrollbar-width:thin}.scrollbar-thumb-cyan-500\\\\\\\\/30::-webkit-scrollbar-thumb {background-color:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}.scrollbar-track-transparent::-webkit-scrollbar-track{background-color:transparent}::-webkit-scrollbar{width:6px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}::-webkit-scrollbar-thumb:hover{background:rgba(6,182,212,.5)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicLiveConversationPage, \"bsIEZR9K7LoW29+ic6ycO1/HOKk=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c1 = PublicLiveConversationPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"CustomerIcon\");\n$RefreshReg$(_c1, \"PublicLiveConversationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/live/[uuid].tsx\n"));

/***/ }),

/***/ "./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: function() { return /* binding */ ApiClient; },\n/* harmony export */   agentApi: function() { return /* binding */ agentApi; },\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; },\n/* harmony export */   conversationApi: function() { return /* binding */ conversationApi; },\n/* harmony export */   customerApi: function() { return /* binding */ customerApi; },\n/* harmony export */   imageApi: function() { return /* binding */ imageApi; },\n/* harmony export */   orderApi: function() { return /* binding */ orderApi; },\n/* harmony export */   productApi: function() { return /* binding */ productApi; },\n/* harmony export */   storeApi: function() { return /* binding */ storeApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"./src/utils/auth.ts\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\n// Helper function to handle API responses\nasync function handleResponse(response) {\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || \"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n}\n// Generic API client class\nclass ApiClient {\n    // Generic GET request\n    async get(endpoint, params) {\n        const url = new URL(\"\".concat(this.baseUrl).concat(endpoint));\n        if (params) {\n            Object.entries(params).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        console.log(\"API GET request to:\", url.toString());\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url.toString(), {\n            method: \"GET\"\n        });\n        return handleResponse(response);\n    }\n    // Generic POST request\n    async post(endpoint, data) {\n        const url = \"\".concat(this.baseUrl).concat(endpoint);\n        console.log(\"\\uD83D\\uDD27 API POST request to:\", url);\n        console.log(\"\\uD83D\\uDD27 API POST data:\", data);\n        console.log(\"\\uD83D\\uDD27 API base URL:\", this.baseUrl);\n        try {\n            const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url, {\n                method: \"POST\",\n                body: data ? JSON.stringify(data) : undefined\n            });\n            console.log(\"\\uD83D\\uDD27 API POST response status:\", response.status);\n            console.log(\"\\uD83D\\uDD27 API POST response headers:\", Object.fromEntries(response.headers.entries()));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"❌ API POST error response:\", errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \", body: \").concat(errorText));\n            }\n            return handleResponse(response);\n        } catch (error) {\n            console.error(\"❌ API POST fetch error:\", error);\n            throw error;\n        }\n    }\n    // Generic PUT request\n    async put(endpoint, data) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"\".concat(this.baseUrl).concat(endpoint), {\n            method: \"PUT\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return handleResponse(response);\n    }\n    // Generic DELETE request\n    async delete(endpoint) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"\".concat(this.baseUrl).concat(endpoint), {\n            method: \"DELETE\"\n        });\n        return handleResponse(response);\n    }\n    constructor(baseUrl = API_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n}\n// Create a default API client instance\nconst apiClient = new ApiClient();\n// Store API methods\nconst storeApi = {\n    getAll: (params)=>apiClient.get(\"/stores\", params),\n    getById: (id)=>apiClient.get(\"/stores/\".concat(id)),\n    getByUserId: (userId, params)=>apiClient.get(\"/stores/user/\".concat(userId), params),\n    getByUuid: (uuid)=>apiClient.get(\"/stores/uuid/\".concat(uuid)),\n    create: (data)=>apiClient.post(\"/stores\", data),\n    update: (id, data)=>apiClient.put(\"/stores/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/stores/\".concat(id))\n};\n// Product API methods\nconst productApi = {\n    getAll: (params)=>apiClient.get(\"/products\", params),\n    getById: (id)=>apiClient.get(\"/products/\".concat(id)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/products/store/\".concat(storeId), params),\n    getByStoreUuid: (storeUuid, params)=>apiClient.get(\"/products/store/uuid/\".concat(storeUuid), params),\n    create: (data)=>apiClient.post(\"/products\", data),\n    update: (id, data)=>apiClient.put(\"/products/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/products/\".concat(id))\n};\n// Customer API methods\nconst customerApi = {\n    getAll: (params)=>apiClient.get(\"/customers\", params),\n    getById: (id)=>apiClient.get(\"/customers/\".concat(id)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/customers/store/\".concat(storeId), params),\n    create: (data)=>apiClient.post(\"/customers\", data),\n    update: (id, data)=>apiClient.put(\"/customers/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/customers/\".concat(id))\n};\n// User API methods\nconst userApi = {\n    getAll: (params)=>apiClient.get(\"/users\", params),\n    getById: (id)=>apiClient.get(\"/users/\".concat(id)),\n    create: (data)=>apiClient.post(\"/users\", data),\n    update: (id, data)=>apiClient.put(\"/users/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/users/\".concat(id))\n};\n// Order API methods\nconst orderApi = {\n    getAll: (params)=>apiClient.get(\"/orders\", params),\n    getById: (id)=>apiClient.get(\"/orders/\".concat(id)),\n    getByOrderNumber: (orderNumber)=>apiClient.get(\"/orders/order-number/\".concat(orderNumber)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/orders/store/\".concat(storeId), params),\n    filter: (data)=>apiClient.post(\"/orders/filter\", data),\n    create: (data)=>apiClient.post(\"/orders\", data),\n    update: (id, data)=>apiClient.put(\"/orders/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/orders/\".concat(id))\n};\n// Conversation API methods (using existing endpoints)\nconst conversationApi = {\n    getAll: (params)=>apiClient.get(\"/conversations\", params),\n    getById: (id)=>apiClient.get(\"/conversations/\".concat(id)),\n    getByUuid: (uuid)=>apiClient.get(\"/conversations/uuid/\".concat(uuid)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/conversations/store/\".concat(storeId), params),\n    getByUserId: (userId, params)=>apiClient.get(\"/conversations/user/\".concat(userId), params),\n    create: (data)=>apiClient.post(\"/conversations\", data),\n    getTimeline: (id, params)=>apiClient.get(\"/conversations/\".concat(id, \"/timeline\"), params),\n    getUnifiedTimeline: (id, params)=>apiClient.get(\"/conversations/\".concat(id, \"/unified-timeline\"), params),\n    getUnifiedTimelineByUuid: (uuid, params)=>apiClient.get(\"/conversations/uuid/\".concat(uuid, \"/unified-timeline\"), params),\n    appendMessage: (id, data)=>apiClient.post(\"/conversations/\".concat(id, \"/messages\"), data),\n    appendMessageByUuid: (uuid, data)=>apiClient.post(\"/conversations/uuid/\".concat(uuid, \"/messages\"), data)\n};\n// Image API methods\nconst imageApi = {\n    upload: (file)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"/images\", {\n            method: \"POST\",\n            body: formData\n        }).then((response)=>{\n            if (!response.ok) {\n                return response.json().then((errorData)=>{\n                    throw new Error(errorData.error || \"Failed to upload image\");\n                });\n            }\n            return response.json();\n        });\n    }\n};\n// Agent API methods (using existing endpoints)\nconst agentApi = {\n    getAll: (params)=>apiClient.get(\"/agents\", params),\n    getById: (id)=>apiClient.get(\"/agents/\".concat(id)),\n    create: (data)=>apiClient.post(\"/agents\", data),\n    update: (id, data)=>apiClient.put(\"/agents/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/agents/\".concat(id)),\n    generateMessage: (data)=>apiClient.post(\"/agents/runtime/generate-message\", data)\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/api.ts\n"));

/***/ }),

/***/ "./node_modules/next/dist/compiled/client-only/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/client-only/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {



/***/ }),

/***/ "./node_modules/next/dist/compiled/process/browser.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/process/browser.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/compiled/process/browser.js\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&page=%2Flive%2F%5Buuid%5D!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);