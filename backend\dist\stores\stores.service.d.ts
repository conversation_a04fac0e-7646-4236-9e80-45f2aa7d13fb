import { Repository } from 'typeorm';
import { Store } from './store.entity';
interface CreateStoreDto {
    name: string;
    description?: string;
    currency?: string;
    preferredLanguage?: string;
    userId: string | number;
}
export declare class StoresService {
    private storesRepository;
    constructor(storesRepository: Repository<Store>);
    findAll(): Promise<Store[]>;
    findOne(id: string): Promise<Store>;
    findByUuid(uuid: string): Promise<Store>;
    findByUserId(userId: string): Promise<Store[]>;
    create(createStoreDto: CreateStoreDto): Promise<Store>;
    update(id: string, updateStoreDto: Partial<Store>): Promise<Store>;
    remove(id: string): Promise<void>;
}
export {};
