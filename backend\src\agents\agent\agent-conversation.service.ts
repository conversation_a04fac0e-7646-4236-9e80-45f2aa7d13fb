import { DataSource } from 'typeorm';
import {
	AgentRuntimeCallbacks,
	ConversationNotification,
	GenerateMessageInput,
	ProcessUserMessageInput,
	buildConversationAgent,
	buildProductContextMessage,
	buildCustomerContextMessage,
	ensureLLMProviderConfigured,
	toBigInt,
} from './conversation-agent';

import { LlmApi, type Message as AgentMessage } from '../../../../ai-agent/dist';
import { createToolCall } from './tool-call.service';
import { Conversation } from '../entities/Conversation';
import { Store } from '../entities/Store';
import { Message } from '../entities/Message';
import { Product } from '../entities/Product';
import { Customer } from '../entities/Customer';
import { ToolCall } from '../entities/ToolCall';

// ---- Internal helpers (keep file-local; no exports) ----
async function getConversationByUuid(db: DataSource, conversationUuid: string) {
	const conversationRepository = db.getRepository(Conversation);
	return conversationRepository.findOne({
		where: { uuid: conversationUuid, isDeleted: false },
		select: ['id', 'storeId'],
	});
}

async function getStoreCurrency(db: DataSource, storeId: bigint) {
	const storeRepository = db.getRepository(Store);
	const store = await storeRepository.findOne({
		where: { id: storeId.toString(), isDeleted: false },
		select: ['currency'],
	});
	return store?.currency || 'USD';
}

async function getStorePreferredLanguage(db: DataSource, storeId: bigint) {
	const storeRepository = db.getRepository(Store);
	const store = await storeRepository.findOne({
		where: { id: storeId.toString(), isDeleted: false },
		select: ['preferredLanguage'],
	});
	return store?.preferredLanguage || 'en';
}

function mapHistoryToAgentMessages(historyRecords: Array<{ content: string; agentId: string | null; userId?: any; customerId?: any }>): AgentMessage[] {
	return historyRecords.map((m) => ({
		role: m.agentId ? 'assistant' : 'user',
		content: m.content,
	}));
}

// Helper function to extract metrics from LlmResponseResult
function extractMetricsFromResponse(response: any) {
	return {
		totalCost: response.totalCost || 0,
		totalInputTokens: response.totalInputTokens || 0,
		totalOutputTokens: response.totalOutputTokens || 0,
		totalExecutionTime: response.totalExecutionTime || 0,
	};
}

// Helper function to convert Decimal fields to numbers for database operations
function convertDecimalToNumber(value: any): number | null {
	if (value === null || value === undefined) return null;
	return Number(value);
}

// Helper function to update conversation totals
async function updateConversationTotals(db: DataSource, conversationId: bigint) {
	try {
		const messageRepository = db.getRepository(Message);
		const toolCallRepository = db.getRepository(ToolCall);
		const conversationRepository = db.getRepository(Conversation);

		// Get all messages and tool calls for this conversation
		const [messages, toolCalls] = await Promise.all([
			messageRepository.find({
				where: { conversationId: conversationId.toString(), isDeleted: false },
				select: ['cost', 'executionTime', 'inputTokens', 'outputTokens'],
			}),
			toolCallRepository.find({
				where: { conversationId: conversationId.toString(), isDeleted: false },
				select: ['cost', 'executionTime', 'inputTokens', 'outputTokens'],
			}),
		]);

		// Calculate totals from messages and tool calls
		const totalCost = [...messages, ...toolCalls].reduce((sum, item) => sum + (Number(item.cost) || 0), 0);
		const totalExecutionTime = [...messages, ...toolCalls].reduce((sum, item) => sum + (item.executionTime || 0), 0);
		const totalInputTokens = [...messages, ...toolCalls].reduce((sum, item) => sum + (item.inputTokens || 0), 0);
		const totalOutputTokens = [...messages, ...toolCalls].reduce((sum, item) => sum + (item.outputTokens || 0), 0);

		// Update conversation with totals
		await conversationRepository.update(
			{ id: conversationId.toString() },
			{
				totalCost,
				totalExecutionTime,
				totalInputTokens,
				totalOutputTokens,
			}
		);
	} catch (error) {
		console.warn('Failed to update conversation totals:', error);
	}
}

// Helper function to persist agent response to database
async function persistAgentResponse(params: {
	db: DataSource;
	agentId: string;
	ownerUserId: string | number | bigint;
	conversationId: bigint;
	response: string;
	toolCalls?: Array<{
		id: string;
		function: {
			name: string;
			arguments: string;
		};
	}>;
	toolOutputs?: Array<{
		role: string;
		content: string;
		tool_call_id: string;
	}>;
	executionDetails?: Array<{
		toolCallId: string;
		toolName: string;
		executionTime: number;
		success: boolean;
		errorMessage?: string;
		startTime: number;
		endTime: number;
		cost?: number;
		inputTokens?: number;
		outputTokens?: number;
	}>;
	llmCosts?: {
		totalCost: number;
		totalInputTokens: number;
		totalOutputTokens: number;
		totalExecutionTime?: number;
	};
}): Promise<{ content: string }> {
	const { db, agentId, ownerUserId, conversationId, response, toolCalls, toolOutputs, executionDetails, llmCosts } = params;

	const messageRepository = db.getRepository(Message);
	const toolCallRepository = db.getRepository(ToolCall);

	// Get the next unified sequence number for this conversation (checking both messages and tool calls)
	const [lastMessage, lastToolCall] = await Promise.all([
		messageRepository.findOne({
			where: { conversationId: conversationId.toString(), isDeleted: false },
			order: { sequence: 'DESC' },
			select: ['sequence'],
		}),
		toolCallRepository.findOne({
			where: { conversationId: conversationId.toString(), isDeleted: false },
			order: { sequence: 'DESC' },
			select: ['sequence'],
		}),
	]);

	const maxSequence = Math.max(
		lastMessage?.sequence ?? 0,
		lastToolCall?.sequence ?? 0
	);
	const nextSequence = maxSequence + 1;

	if (toolCalls && toolCalls.length > 0) {
		for (let i = 0; i < toolCalls.length; i++) {
			const toolCall = toolCalls[i];
			const toolOutput = toolOutputs?.find(to => to.tool_call_id === toolCall.id);
			const executionDetail = executionDetails?.find(ed => ed.toolCallId === toolCall.id);
			
			// Parse tool arguments
			let toolInput: any = {};
			try {
				toolInput = JSON.parse(toolCall.function.arguments);
			} catch (e) {
				toolInput = { raw: toolCall.function.arguments };
			}

			// Handle tool output - it might not be valid JSON
			let toolOutputData: any = undefined;
			if (toolOutput?.content) {
				try {
					// Try to parse as JSON first
					toolOutputData = JSON.parse(toolOutput.content);
				} catch (e) {
					// If it's not JSON, store as a string
					toolOutputData = { rawContent: toolOutput.content };
				}
			}

			// Create tool call record with the same sequence number
			try {
				await toolCallRepository.save({
					toolName: toolCall.function.name,
					toolInput,
					conversationId: conversationId.toString(),
					createdBy: toBigInt(ownerUserId).toString(),
					toolOutput: toolOutputData,
					executionTime: executionDetail?.executionTime,
					success: executionDetail?.success ?? true,
					errorMessage: executionDetail?.errorMessage,
					cost: executionDetail?.cost,
					inputTokens: executionDetail?.inputTokens,
					outputTokens: executionDetail?.outputTokens,
					sequence: nextSequence + i, // Increment sequence for each tool call
				});
			} catch (toolCallError) {
				// Continue with other tool calls even if one fails
				console.warn('Failed to create tool call record:', toolCallError);
			}
		}
	}

	try {
		// Create the agent message with sequence number after all tool calls
		const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
		const created = await messageRepository.save({
			content: response,
			agentId,
			createdBy: toBigInt(ownerUserId).toString(),
			conversationId: conversationId.toString(),
			sequence: messageSequence,
			// Cost tracking fields
			cost: llmCosts?.totalCost,
			executionTime: llmCosts?.totalExecutionTime,
			inputTokens: llmCosts?.totalInputTokens,
			outputTokens: llmCosts?.totalOutputTokens,
		});

		// Update conversation totals after creating message and tool calls
		await updateConversationTotals(db, conversationId);

		return created;
	} catch (error) {
		console.error('Failed to persist agent response:', error);

		// Try to create with minimal data as fallback
		try {
			const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
			const fallbackMessage = await messageRepository.save({
				content: response || 'I apologize, but I encountered an error while processing your request.',
				agentId,
				createdBy: toBigInt(ownerUserId).toString(),
				conversationId: conversationId.toString(),
				sequence: messageSequence, // Use the sequence number after tool calls
				// Cost tracking fields
				cost: llmCosts?.totalCost,
				executionTime: llmCosts?.totalExecutionTime,
				inputTokens: llmCosts?.totalInputTokens,
				outputTokens: llmCosts?.totalOutputTokens,
			});

			// Update conversation totals even for fallback message
			await updateConversationTotals(db, conversationId);

			return fallbackMessage;
		} catch (fallbackError) {
			console.error('Fallback message creation also failed:', fallbackError);
			throw new Error(`Failed to save agent response: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
		}
	}
}

// Helper function to safely update notification status
async function updateNotificationSafely(
	db: DataSource,
	conversationUuid: string,
	notification: ConversationNotification,
	callbacks?: AgentRuntimeCallbacks
): Promise<void> {
	try {
		const conversationRepository = db.getRepository(Conversation);
		// Update the database
		await conversationRepository.update(
			{ uuid: conversationUuid },
			{ notificationStatus: notification }
		);

		// Call the callback if provided
		await callbacks?.setNotification?.(conversationUuid, notification);
	} catch (error) {
		console.warn(`Failed to update notification status to ${notification}:`, error);
	}
}

// Helper function to get conversation and store information together
async function getConversationAndStoreInfo(db: DataSource, conversationUuid: string) {
	const conversation = await getConversationByUuid(db, conversationUuid);
	if (!conversation) {
		throw new Error(`Conversation with UUID ${conversationUuid} not found`);
	}

	const storeCurrency = await getStoreCurrency(db, BigInt(conversation.storeId));
	const storePreferredLanguage = await getStorePreferredLanguage(db, BigInt(conversation.storeId));

	return {
		conversation,
		storeCurrency,
		storePreferredLanguage
	};
}

// Helper function to get conversation history
async function getConversationHistory(db: DataSource, conversationId: bigint): Promise<AgentMessage[]> {
	const messageRepository = db.getRepository(Message);
	const messageHistory = await messageRepository.find({
		where: { conversationId: conversationId.toString(), isDeleted: false },
		order: { createdAt: 'ASC' },
		select: ['content', 'agentId', 'userId', 'customerId'],
	});

	return mapHistoryToAgentMessages(messageHistory);
}

// Helper function to get products for a store
async function getStoreProducts(db: DataSource, storeId: bigint): Promise<Array<{ id: bigint; name: string; description: string | null; price: number; sku: string | null }>> {
	const productRepository = db.getRepository(Product);
	const products = await productRepository.find({
		where: {
			storeId: storeId.toString(),
			isDeleted: false
		},
		select: ['id', 'name', 'description', 'price', 'sku'],
		order: { createdAt: 'DESC' },
		// Limit to prevent overwhelming the context
		take: 50
	});

	return products.map(product => ({
		id: BigInt(product.id),
		name: product.name,
		description: product.description,
		price: Number(product.price),
		sku: product.sku
	}));
}

// Helper function to get customer information if conversation has a customer
async function getConversationCustomer(db: DataSource, conversationUuid: string): Promise<{ name: string | null; email: string | null; phone: string | null; address: string | null } | null> {
	const conversationRepository = db.getRepository(Conversation);
	const conversation = await conversationRepository.findOne({
		where: { uuid: conversationUuid, isDeleted: false },
		select: ['id', 'customerId']
	});

	if (!conversation?.customerId) {
		return null;
	}

	const customerRepository = db.getRepository(Customer);
	const customer = await customerRepository.findOne({
		where: { id: conversation.customerId, isDeleted: false },
		select: ['name', 'email', 'phone', 'address']
	});

	return customer;
}

export async function generateMessage(db: DataSource, input: GenerateMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string> {
	await ensureLLMProviderConfigured();

	const startTime = Date.now();

	// Get conversation and store information
	const { conversation, storeCurrency, storePreferredLanguage } = await getConversationAndStoreInfo(db, input.conversationUuid);

	// Get products for the store
	const products = await getStoreProducts(db, BigInt(conversation.storeId));

	// Get customer information if conversation has a customer
	const customer = await getConversationCustomer(db, input.conversationUuid);

	// Build agent with basic context (no products/customer in system prompt)
	const agent = buildConversationAgent(db, input.conversationUuid, storeCurrency, storePreferredLanguage);

	// Get conversation history
	const mapped = await getConversationHistory(db, BigInt(conversation.id));

	// Build context messages
	const productContextMessage = buildProductContextMessage(products, storeCurrency);
	const customerContextMessage = buildCustomerContextMessage(customer);

	// Combine context messages with conversation history
	const allMessages: AgentMessage[] = [];
	
	// Add context messages (they now always return a message)
	if (productContextMessage) allMessages.push(productContextMessage);
	if (customerContextMessage) allMessages.push(customerContextMessage);
	
	// Add conversation history
	allMessages.push(...mapped);
	
	// Add the current user input
	allMessages.push({ role: 'user', content: 'Continue the conversation.' });

	const response = await LlmApi.generateLlmResponse(agent, allMessages);
	
	const content = response.content;
	const executionTime = Date.now() - startTime;

	// Extract metrics from the response
	const metrics = extractMetricsFromResponse(response);

	// Persist as agent message using the helper function with tool call information
	await persistAgentResponse({
		db,
		agentId: input.agentId,
		ownerUserId: input.ownerUserId,
		conversationId: BigInt(conversation.id),
		response: content,
		toolCalls: response.calls,
		toolOutputs: response.outputs,
		executionDetails: response.executionDetails,
		llmCosts: {
			...metrics,
			totalExecutionTime: executionTime,
		},
	});

	return content;
}

export async function processUserMessage(
	db: DataSource,
	params: ProcessUserMessageInput,
	callbacks?: AgentRuntimeCallbacks
): Promise<string> {
	console.log(`🔧 processUserMessage called with:`, {
		dbType: typeof db,
		dbExists: !!db,
		dbInitialized: db?.isInitialized,
		params,
		timestamp: new Date().toISOString()
	});

	await ensureLLMProviderConfigured();
	const { conversationUuid, userMessage, ownerUserId, agentId } = params;

	const startTime = Date.now();

	try {
		// Validate database connection is still valid
		if (!db || !db.isInitialized) {
			console.error(`❌ Database connection validation failed in processUserMessage:`, {
				dbExists: !!db,
				dbInitialized: db?.isInitialized,
				dbType: typeof db
			});
			throw new Error('Database connection is not properly initialized in processUserMessage');
		}

		console.log(`✅ Database connection validated in processUserMessage`);

		// Set initial thinking notification
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.AgentIsThinking, callbacks);

		// Get conversation and store information
		const { conversation, storeCurrency, storePreferredLanguage } = await getConversationAndStoreInfo(db, conversationUuid);

		// Get products for the store
		const products = await getStoreProducts(db, BigInt(conversation.storeId));

		// Get customer information if conversation has a customer
		// const customer = await getConversationCustomer(db, conversationUuid);
		
		// Placeholder: empty customer for now
		const customer = null;

		// Build conversation agent with basic context (no products/customer in system prompt)
		console.log(`🔧 Building conversation agent with database connection:`, {
			dbType: typeof db,
			dbInitialized: db?.isInitialized,
			conversationUuid,
			storeCurrency,
			storePreferredLanguage
		});
		const conversationAgent = buildConversationAgent(db, conversationUuid, storeCurrency, storePreferredLanguage);

		// Get conversation history
		const history = await getConversationHistory(db, BigInt(conversation.id));

		// Build context messages
		const productContextMessage = buildProductContextMessage(products, storeCurrency);
		const customerContextMessage = buildCustomerContextMessage(customer);

		// Combine context messages with conversation history
		const allMessages: AgentMessage[] = [];
		
		// Add context messages (they now always return a message)
		if (productContextMessage) allMessages.push(productContextMessage);
		if (customerContextMessage) allMessages.push(customerContextMessage);
		
		// Add conversation history
		allMessages.push(...history);
		
		// Add the current user message
		allMessages.push({ role: 'user', content: userMessage });

		// Update notification to show response generation
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.AgentIsGeneratingResponse, callbacks);
		
		// Generate response using the conversation agent
		const response = await LlmApi.generateLlmResponse(conversationAgent, allMessages);
		
		// const sanitizedResponse = sanitizeAssistantReply(response.content);
		const sanitizedResponse = response.content;

		const executionTime = Date.now() - startTime;

		// Extract metrics from the response
		const metrics = extractMetricsFromResponse(response);

		// Persist the final response with tool call information
		const created = await persistAgentResponse({
			db,
			agentId,
			ownerUserId,
			conversationId: BigInt(conversation.id),
			response: sanitizedResponse,
			toolCalls: response.calls,
			toolOutputs: response.outputs,
			executionDetails: response.executionDetails,
			llmCosts: {
				...metrics,
				totalExecutionTime: executionTime,
			},
		});

		// Clear notification status
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.None, callbacks);

		return created.content;
	} catch (error) {
		// Ensure notification is cleared on any error
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.None, callbacks);
		throw error;
	}
}


