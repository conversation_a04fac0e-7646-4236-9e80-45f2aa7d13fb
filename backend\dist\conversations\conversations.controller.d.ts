import { ConversationsService } from './conversations.service';
import { Conversation } from './conversation.entity';
export declare class ConversationsController {
    private readonly conversationsService;
    constructor(conversationsService: ConversationsService);
    create(createConversationDto: Partial<Conversation> & {
        userId?: string | number;
        storeId?: string | number;
        createdBy?: string | number;
    }): Promise<Conversation>;
    findAll(): Promise<Conversation[]>;
    findByStoreId(storeId: string): Promise<Conversation[]>;
    findByUserId(userId: string): Promise<Conversation[]>;
    findByUuid(uuid: string): Promise<Conversation>;
    getUnifiedTimelineByUuid(uuid: string, page?: string, limit?: string): Promise<any>;
    getTimeline(id: string, page?: string, limit?: string): Promise<any>;
    getUnifiedTimeline(id: string, page?: string, limit?: string): Promise<any>;
    findOne(id: string): Promise<Conversation>;
    update(id: string, updateConversationDto: Partial<Conversation>): Promise<Conversation>;
    remove(id: string): Promise<void>;
}
