"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolCallsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const tool_calls_service_1 = require("./tool-calls.service");
let ToolCallsController = class ToolCallsController {
    constructor(toolCallsService) {
        this.toolCallsService = toolCallsService;
    }
    create(createToolCallDto) {
        return this.toolCallsService.create(createToolCallDto);
    }
    findAll() {
        return this.toolCallsService.findAll();
    }
    findByConversationId(conversationId) {
        return this.toolCallsService.findByConversationId(conversationId);
    }
    findOne(id) {
        return this.toolCallsService.findOne(id);
    }
    update(id, updateToolCallDto) {
        return this.toolCallsService.update(id, updateToolCallDto);
    }
    remove(id) {
        return this.toolCallsService.remove(id);
    }
};
exports.ToolCallsController = ToolCallsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new tool call' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Tool call created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ToolCallsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all tool calls' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tool calls retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ToolCallsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('conversation/:conversationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get tool calls by conversation ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tool calls retrieved successfully' }),
    __param(0, (0, common_1.Param)('conversationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ToolCallsController.prototype, "findByConversationId", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a tool call by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tool call retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Tool call not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ToolCallsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a tool call' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tool call updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Tool call not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ToolCallsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a tool call' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tool call deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Tool call not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ToolCallsController.prototype, "remove", null);
exports.ToolCallsController = ToolCallsController = __decorate([
    (0, swagger_1.ApiTags)('tool-calls'),
    (0, common_1.Controller)('tool-calls'),
    __metadata("design:paramtypes", [tool_calls_service_1.ToolCallsService])
], ToolCallsController);
//# sourceMappingURL=tool-calls.controller.js.map