import { Repository } from 'typeorm';
import { Conversation } from './conversation.entity';
import { Message } from './message.entity';
export declare class ConversationsService {
    private conversationsRepository;
    private messagesRepository;
    constructor(conversationsRepository: Repository<Conversation>, messagesRepository: Repository<Message>);
    findAll(): Promise<Conversation[]>;
    findOne(id: string): Promise<Conversation>;
    findByStoreId(storeId: string): Promise<Conversation[]>;
    findByUserId(userId: string): Promise<Conversation[]>;
    findByUuid(uuid: string): Promise<Conversation>;
    create(createConversationDto: Partial<Conversation> & {
        userId?: string | number;
        storeId?: string | number;
        createdBy?: string | number;
    }): Promise<Conversation>;
    update(id: string, updateConversationDto: Partial<Conversation>): Promise<Conversation>;
    remove(id: string): Promise<void>;
    getUnifiedTimeline(id: string, page?: number, limit?: number): Promise<any>;
    getUnifiedTimelineByUuid(uuid: string, page?: number, limit?: number): Promise<any>;
    getTimeline(id: string, page?: number, limit?: number): Promise<any>;
    addMessage(conversationId: string, messageData: Partial<Message> & {
        content: string;
        createdBy: string;
        agentId?: string;
        userId?: string;
        customerId?: string;
        imageUrl?: string;
        videoUrl?: string;
        attachmentUrl?: string;
        attachmentType?: string;
        cost?: number;
        executionTime?: number;
        inputTokens?: number;
        outputTokens?: number;
    }): Promise<Message>;
    addMessageByUuid(uuid: string, messageData: Partial<Message> & {
        content: string;
        createdBy: string;
        agentId?: string;
        userId?: string;
        customerId?: string;
        imageUrl?: string;
        videoUrl?: string;
        attachmentUrl?: string;
        attachmentType?: string;
        cost?: number;
        executionTime?: number;
        inputTokens?: number;
        outputTokens?: number;
    }): Promise<Message>;
}
