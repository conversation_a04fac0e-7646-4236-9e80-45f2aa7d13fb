import { StoresService } from './stores.service';
import { Store } from './store.entity';
import { PaginatedResponse } from '../types/pagination';
interface CreateStoreDto {
    name: string;
    description?: string;
    currency?: string;
    preferredLanguage?: string;
    userId: string | number;
}
export declare class StoresController {
    private readonly storesService;
    constructor(storesService: StoresService);
    create(createStoreDto: CreateStoreDto): Promise<Store>;
    findAll(page?: string, limit?: string): Promise<PaginatedResponse<Store>>;
    findByUserId(userId: string, page?: string, limit?: string): Promise<PaginatedResponse<Store>>;
    findByUuid(uuid: string): Promise<Store>;
    findOne(id: string): Promise<Store>;
    update(id: string, updateStoreDto: Partial<Store>): Promise<Store>;
    remove(id: string): Promise<void>;
}
export {};
