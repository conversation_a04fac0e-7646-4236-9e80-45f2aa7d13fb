"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.successResponse = successResponse;
exports.errorResponse = errorResponse;
exports.createPaginationMeta = createPaginationMeta;
function successResponse(data, meta) {
    return {
        success: true,
        data,
        ...(meta && { meta })
    };
}
function errorResponse(message, statusCode = 500, details) {
    return {
        success: false,
        error: message,
        ...(details && { details })
    };
}
function createPaginationMeta(page, limit, total) {
    const totalPages = Math.ceil(total / limit);
    return {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
    };
}
//# sourceMappingURL=api-response.js.map