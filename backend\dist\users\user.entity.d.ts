import { UserRole, UserStatus } from '../shared/enums';
export declare class User {
    id: string;
    email: string;
    name: string;
    image: string;
    role: UserRole;
    status: UserStatus;
    phone: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    lastLoginAt: Date;
    isDeleted: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy: string;
    ownedStores: any[];
    managedStores: any[];
    orders: any[];
    createdOrders: any[];
    updatedOrders: any[];
    createdOrderItems: any[];
    updatedOrderItems: any[];
    createdProducts: any[];
    updatedProducts: any[];
    createdCustomers: any[];
    updatedCustomers: any[];
    createdStores: any[];
    updatedStores: any[];
    conversations: any[];
    messages: any[];
    createdAgents: any[];
    updatedAgents: any[];
    toolCalls: any[];
    createdToolCalls: any[];
    updatedToolCalls: any[];
    posts: any[];
    createdPosts: any[];
    updatedPosts: any[];
    accounts: any[];
    sessions: any[];
    verificationTokens: any[];
}
