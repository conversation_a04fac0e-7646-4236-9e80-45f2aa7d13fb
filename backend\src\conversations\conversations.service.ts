import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Conversation } from './conversation.entity';
import { Message } from './message.entity';
import { generateUUID7 } from '../utils/uuid';

@Injectable()
export class ConversationsService {
  constructor(
    @InjectRepository(Conversation)
    private conversationsRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private messagesRepository: Repository<Message>,
  ) {}

  async findAll(): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Conversation> {
    const conversation = await this.conversationsRepository.findOne({
      where: { id, isDeleted: false },
    });
    
    if (!conversation) {
      throw new NotFoundException(`Conversation with ID ${id} not found`);
    }
    
    return conversation;
  }

  async findByStoreId(storeId: string): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { storeId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findByUserId(userId: string): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { userId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findByUuid(uuid: string): Promise<Conversation> {
    const conversation = await this.conversationsRepository.findOne({
      where: { uuid, isDeleted: false },
    });
    
    if (!conversation) {
      throw new NotFoundException(`Conversation with UUID ${uuid} not found`);
    }
    
    return conversation;
  }

  async create(createConversationDto: Partial<Conversation> & { userId?: string | number; storeId?: string | number; createdBy?: string | number }): Promise<Conversation> {
    // Extract userId and map it to the correct fields
    const { userId, storeId, createdBy, ...conversationData } = createConversationDto;

    console.log('Creating conversation with data:', {
      userId,
      storeId,
      createdBy,
      conversationData,
      userIdString: userId?.toString(),
      storeIdString: storeId?.toString()
    });

    // Create the conversation with proper field mapping and auto-generated UUID
    const conversation = this.conversationsRepository.create({
      ...conversationData,
      uuid: generateUUID7(), // Auto-generate UUID7 for the conversation
      userId: userId?.toString(), // Map userId to the entity field
      storeId: storeId?.toString(), // Ensure storeId is properly mapped
      createdBy: (createdBy || userId)?.toString(), // Use createdBy if provided, otherwise use userId
    });

    console.log('Created conversation object:', conversation);

    return this.conversationsRepository.save(conversation);
  }

  async update(id: string, updateConversationDto: Partial<Conversation>): Promise<Conversation> {
    const conversation = await this.findOne(id);
    Object.assign(conversation, updateConversationDto);
    return this.conversationsRepository.save(conversation);
  }

  async remove(id: string): Promise<void> {
    const conversation = await this.findOne(id);
    conversation.isDeleted = true;
    await this.conversationsRepository.save(conversation);
  }

  async getUnifiedTimeline(id: string, page: number = 1, limit: number = 50): Promise<any> {
    const conversation = await this.findOne(id);

    // Get messages for this conversation
    const [messages, total] = await this.messagesRepository.findAndCount({
      where: {
        conversationId: id,
        isDeleted: false
      },
      order: { createdAt: 'ASC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Transform messages into timeline format
    const timeline = messages.map(message => ({
      id: message.id,
      type: 'message',
      content: message.content,
      role: message.role,
      metadata: message.metadata,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      userId: message.userId,
      conversationId: message.conversationId,
    }));

    return {
      timeline,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getUnifiedTimelineByUuid(uuid: string, page: number = 1, limit: number = 50): Promise<any> {
    const conversation = await this.findByUuid(uuid);
    return this.getUnifiedTimeline(conversation.id, page, limit);
  }

  async getTimeline(id: string, page: number = 1, limit: number = 50): Promise<any> {
    // For now, timeline and unified timeline are the same
    // In the future, timeline might include additional events like tool calls, status changes, etc.
    return this.getUnifiedTimeline(id, page, limit);
  }

  async addMessage(conversationId: string, messageData: Partial<Message> & {
    content: string;
    createdBy: string;
    agentId?: string;
    userId?: string;
    customerId?: string;
    imageUrl?: string;
    videoUrl?: string;
    attachmentUrl?: string;
    attachmentType?: string;
    cost?: number;
    executionTime?: number;
    inputTokens?: number;
    outputTokens?: number;
  }): Promise<Message> {
    // Verify conversation exists
    const conversation = await this.findOne(conversationId);

    // Determine role based on the message data
    let role = 'user'; // default role
    if (messageData.agentId) {
      role = 'assistant';
    } else if (messageData.customerId) {
      role = 'user';
    }

    // Create metadata object from additional fields
    const metadata: any = {};
    if (messageData.agentId) metadata.agentId = messageData.agentId;
    if (messageData.customerId) metadata.customerId = messageData.customerId;
    if (messageData.imageUrl) metadata.imageUrl = messageData.imageUrl;
    if (messageData.videoUrl) metadata.videoUrl = messageData.videoUrl;
    if (messageData.attachmentUrl) metadata.attachmentUrl = messageData.attachmentUrl;
    if (messageData.attachmentType) metadata.attachmentType = messageData.attachmentType;
    if (messageData.cost) metadata.cost = messageData.cost;
    if (messageData.executionTime) metadata.executionTime = messageData.executionTime;
    if (messageData.inputTokens) metadata.inputTokens = messageData.inputTokens;
    if (messageData.outputTokens) metadata.outputTokens = messageData.outputTokens;

    // Create the message
    const message = this.messagesRepository.create({
      content: messageData.content,
      role,
      metadata: Object.keys(metadata).length > 0 ? metadata : null,
      conversationId,
      userId: messageData.userId || messageData.createdBy,
      createdBy: messageData.createdBy,
    });

    return this.messagesRepository.save(message);
  }

  async addMessageByUuid(uuid: string, messageData: Partial<Message> & {
    content: string;
    createdBy: string;
    agentId?: string;
    userId?: string;
    customerId?: string;
    imageUrl?: string;
    videoUrl?: string;
    attachmentUrl?: string;
    attachmentType?: string;
    cost?: number;
    executionTime?: number;
    inputTokens?: number;
    outputTokens?: number;
  }): Promise<Message> {
    // Find conversation by UUID
    const conversation = await this.findByUuid(uuid);

    // Use the conversation ID to add the message
    return this.addMessage(conversation.id, messageData);
  }
}
