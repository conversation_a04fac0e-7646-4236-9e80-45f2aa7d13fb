import { ToolCallsService } from './tool-calls.service';
import { ToolCall } from './tool-call.entity';
export declare class ToolCallsController {
    private readonly toolCallsService;
    constructor(toolCallsService: ToolCallsService);
    create(createToolCallDto: Partial<ToolCall> & {
        userId?: string | number;
    }): Promise<ToolCall>;
    findAll(): Promise<ToolCall[]>;
    findByConversationId(conversationId: string): Promise<ToolCall[]>;
    findOne(id: string): Promise<ToolCall>;
    update(id: string, updateToolCallDto: Partial<ToolCall>): Promise<ToolCall>;
    remove(id: string): Promise<void>;
}
