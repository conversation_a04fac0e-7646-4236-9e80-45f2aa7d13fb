import { DataSourceOptions } from 'typeorm';
export type BigIntSafeDataSourceOptions = DataSourceOptions & {};
export declare function createBigIntSafeDataSourceOptions(baseOptions: DataSourceOptions): BigIntSafeDataSourceOptions;
export declare function createBigIntSafeQueryBuilder<T>(queryBuilder: any, options?: {
    transformResults?: boolean;
    handleCounts?: boolean;
}): any;
export declare function withBigIntHandling<T extends any[], R>(target: any, propertyKey: string, descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R>>): TypedPropertyDescriptor<(...args: T) => Promise<R>>;
export declare function setupBigIntErrorHandling(): void;
export declare function containsBigInt(obj: any): boolean;
export declare function safeClone<T>(obj: T): T;
